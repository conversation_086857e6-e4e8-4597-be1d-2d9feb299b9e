{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfulio\\\\portfolio-react\\\\src\\\\components\\\\Contact.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Contact = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"contact\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"contact-overlay\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"LET'S CREATE TOGETHER\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        action: \"https://formspree.io/f/mblgroaz\",\n        method: \"POST\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"name\",\n          children: \"YOUR NAME\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          id: \"name\",\n          name: \"name\",\n          placeholder: \"NAME\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"email\",\n          children: \"YOUR EMAIL\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          id: \"email\",\n          name: \"email\",\n          placeholder: \"EMAIL\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"services\",\n          children: \"WHAT SERVICES ARE YOU LOOKING FOR?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          id: \"services\",\n          name: \"services\",\n          placeholder: \"Web Design, Graphic design...\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"message\",\n          children: \"YOUR MESSAGE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          id: \"message\",\n          name: \"message\",\n          placeholder: \"YOUR MESSAGE\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"linkedin\",\n          children: [\"Your LinkedIn \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'green'\n            },\n            children: \"(Optional)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 51\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"url\",\n          id: \"linkedin\",\n          name: \"linkedin\",\n          placeholder: \"https://www.linkedin.com/in/your-profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"submit-button\",\n          children: \"SEND\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = Contact;\nexport default Contact;\nvar _c;\n$RefreshReg$(_c, \"Contact\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Contact", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "action", "method", "htmlFor", "type", "id", "name", "placeholder", "required", "style", "color", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/Contact.js"], "sourcesContent": ["import React from 'react';\n\nconst Contact = () => {\n  return (\n    <section className=\"contact\">\n      <div className=\"contact-overlay\">\n        <h2>LET'S CREATE TOGETHER</h2>\n        <form action=\"https://formspree.io/f/mblgroaz\" method=\"POST\">\n          <label htmlFor=\"name\">YOUR NAME</label>\n          <input type=\"text\" id=\"name\" name=\"name\" placeholder=\"NAME\" required />\n          \n          <label htmlFor=\"email\">YOUR EMAIL</label>\n          <input type=\"email\" id=\"email\" name=\"email\" placeholder=\"EMAIL\" required />\n          \n          <label htmlFor=\"services\">WHAT SERVICES ARE YOU LOOKING FOR?</label>\n          <input type=\"text\" id=\"services\" name=\"services\" placeholder=\"Web Design, Graphic design...\" required />\n          \n          <label htmlFor=\"message\">YOUR MESSAGE</label>\n          <textarea id=\"message\" name=\"message\" placeholder=\"YOUR MESSAGE\" required></textarea>\n          \n          <label htmlFor=\"linkedin\">Your LinkedIn <span style={{color: 'green'}}>(Optional)</span></label>\n          <input type=\"url\" id=\"linkedin\" name=\"linkedin\" placeholder=\"https://www.linkedin.com/in/your-profile\" />\n          \n          <button type=\"submit\" className=\"submit-button\">SEND</button>\n        </form>\n      </div>\n    </section>\n  );\n};\n\nexport default Contact;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,OAAO,GAAGA,CAAA,KAAM;EACpB,oBACED,OAAA;IAASE,SAAS,EAAC,SAAS;IAAAC,QAAA,eAC1BH,OAAA;MAAKE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BH,OAAA;QAAAG,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9BP,OAAA;QAAMQ,MAAM,EAAC,iCAAiC;QAACC,MAAM,EAAC,MAAM;QAAAN,QAAA,gBAC1DH,OAAA;UAAOU,OAAO,EAAC,MAAM;UAAAP,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACvCP,OAAA;UAAOW,IAAI,EAAC,MAAM;UAACC,EAAE,EAAC,MAAM;UAACC,IAAI,EAAC,MAAM;UAACC,WAAW,EAAC,MAAM;UAACC,QAAQ;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEvEP,OAAA;UAAOU,OAAO,EAAC,OAAO;UAAAP,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzCP,OAAA;UAAOW,IAAI,EAAC,OAAO;UAACC,EAAE,EAAC,OAAO;UAACC,IAAI,EAAC,OAAO;UAACC,WAAW,EAAC,OAAO;UAACC,QAAQ;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE3EP,OAAA;UAAOU,OAAO,EAAC,UAAU;UAAAP,QAAA,EAAC;QAAkC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpEP,OAAA;UAAOW,IAAI,EAAC,MAAM;UAACC,EAAE,EAAC,UAAU;UAACC,IAAI,EAAC,UAAU;UAACC,WAAW,EAAC,+BAA+B;UAACC,QAAQ;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAExGP,OAAA;UAAOU,OAAO,EAAC,SAAS;UAAAP,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7CP,OAAA;UAAUY,EAAE,EAAC,SAAS;UAACC,IAAI,EAAC,SAAS;UAACC,WAAW,EAAC,cAAc;UAACC,QAAQ;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAErFP,OAAA;UAAOU,OAAO,EAAC,UAAU;UAAAP,QAAA,GAAC,gBAAc,eAAAH,OAAA;YAAMgB,KAAK,EAAE;cAACC,KAAK,EAAE;YAAO,CAAE;YAAAd,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChGP,OAAA;UAAOW,IAAI,EAAC,KAAK;UAACC,EAAE,EAAC,UAAU;UAACC,IAAI,EAAC,UAAU;UAACC,WAAW,EAAC;QAA0C;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEzGP,OAAA;UAAQW,IAAI,EAAC,QAAQ;UAACT,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACW,EAAA,GA1BIjB,OAAO;AA4Bb,eAAeA,OAAO;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}