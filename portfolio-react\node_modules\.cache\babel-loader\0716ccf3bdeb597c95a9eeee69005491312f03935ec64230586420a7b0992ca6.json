{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfulio\\\\portfolio-react\\\\src\\\\components\\\\ClientThoughts.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ClientThoughts = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"client-thoughts\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quote-icon\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"CLIENT THOUGHTS\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"\\\"E-COMMERCE EXPERTISE\\\"\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Will be Available Soon . . . \"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"thoughts-image\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/client touch.jpg\",\n        alt: \"E-commerce Expertise Visual\",\n        style: {\n          width: '100%',\n          maxWidth: '600px',\n          height: 'auto',\n          margin: '20px 0',\n          border: '2px solid #FF2D55',\n          borderRadius: '10px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = ClientThoughts;\nexport default ClientThoughts;\nvar _c;\n$RefreshReg$(_c, \"ClientThoughts\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ClientThoughts", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "style", "width", "max<PERSON><PERSON><PERSON>", "height", "margin", "border", "borderRadius", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/ClientThoughts.js"], "sourcesContent": ["import React from 'react';\n\nconst ClientThoughts = () => {\n  return (\n    <section className=\"client-thoughts\">\n      <div className=\"quote-icon\">\n        {/* Quote icon placeholder */}\n      </div>\n      <h2>CLIENT THOUGHTS</h2>\n      <h3>\"E-COMMERCE EXPERTISE\"</h3>\n      <p>Will be Available Soon . . . </p>\n      <div className=\"thoughts-image\">\n        <img \n          src=\"/client touch.jpg\" \n          alt=\"E-commerce Expertise Visual\" \n          style={{\n            width: '100%', \n            maxWidth: '600px', \n            height: 'auto', \n            margin: '20px 0', \n            border: '2px solid #FF2D55', \n            borderRadius: '10px'\n          }}\n        />\n      </div>\n    </section>\n  );\n};\n\nexport default ClientThoughts;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAC3B,oBACED,OAAA;IAASE,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAClCH,OAAA;MAAKE,SAAS,EAAC;IAAY;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEtB,CAAC,eACNP,OAAA;MAAAG,QAAA,EAAI;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACxBP,OAAA;MAAAG,QAAA,EAAI;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC/BP,OAAA;MAAAG,QAAA,EAAG;IAA6B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eACpCP,OAAA;MAAKE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BH,OAAA;QACEQ,GAAG,EAAC,mBAAmB;QACvBC,GAAG,EAAC,6BAA6B;QACjCC,KAAK,EAAE;UACLC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,QAAQ;UAChBC,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE;QAChB;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACU,EAAA,GAzBIhB,cAAc;AA2BpB,eAAeA,cAAc;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}