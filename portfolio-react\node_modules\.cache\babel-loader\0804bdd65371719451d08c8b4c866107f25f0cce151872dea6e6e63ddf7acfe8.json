{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SkillsTicker=()=>{return/*#__PURE__*/_jsx(\"div\",{className:\"skills-ticker\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"ticker-track\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"UI/UX DESIGN\"}),\" \\u2022 \",/*#__PURE__*/_jsx(\"span\",{children:\"GRAPHIC DESIGN\"}),\" \\u2022 \",/*#__PURE__*/_jsx(\"span\",{children:\"WEB DEVELOPMENT\"}),\" \\u2022 \",/*#__PURE__*/_jsx(\"span\",{children:\"UI/UX DESIGN\"}),\" \\u2022 \",/*#__PURE__*/_jsx(\"span\",{children:\"BRANDING\"}),\" \\u2022 \",/*#__PURE__*/_jsx(\"span\",{children:\"WEBSITE DESIGN\"}),\" \\u2022 \",/*#__PURE__*/_jsx(\"span\",{children:\"DIGITAL CREATIVITY\"}),\" \\u2022 \",/*#__PURE__*/_jsx(\"span\",{children:\"E-COMMERCE SOLUTIONS\"}),\" \\u2022 \",/*#__PURE__*/_jsx(\"span\",{children:\"CUSTOM DEVELOPMENT\"}),/*#__PURE__*/_jsx(\"span\",{children:\"UI/UX DESIGN\"}),\" \\u2022 \",/*#__PURE__*/_jsx(\"span\",{children:\"GRAPHIC DESIGN\"}),\" \\u2022 \",/*#__PURE__*/_jsx(\"span\",{children:\"WEB DEVELOPMENT\"}),\" \\u2022 \",/*#__PURE__*/_jsx(\"span\",{children:\"UI/UX DESIGN\"}),\" \\u2022 \",/*#__PURE__*/_jsx(\"span\",{children:\"BRANDING\"}),\" \\u2022 \",/*#__PURE__*/_jsx(\"span\",{children:\"WEBSITE DESIGN\"}),\" \\u2022 \",/*#__PURE__*/_jsx(\"span\",{children:\"DIGITAL CREATIVITY\"}),\" \\u2022 \",/*#__PURE__*/_jsx(\"span\",{children:\"E-COMMERCE SOLUTIONS\"}),\" \\u2022 \",/*#__PURE__*/_jsx(\"span\",{children:\"CUSTOM DEVELOPMENT\"})]})});};export default SkillsTicker;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "SkillsTicker", "className", "children"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/SkillsTicker.js"], "sourcesContent": ["import React from 'react';\n\nconst SkillsTicker = () => {\n  return (\n    <div className=\"skills-ticker\">\n      <div className=\"ticker-track\">\n        <span>UI/UX DESIGN</span> • <span>GRAPHIC DESIGN</span> • <span>WEB DEVELOPMENT</span> • <span>UI/UX DESIGN</span> • <span>BRANDING</span> • <span>WEBSITE DESIGN</span> • <span>DIGITAL CREATIVITY</span> • <span>E-COMMERCE SOLUTIONS</span> • <span>CUSTOM DEVELOPMENT</span>\n        {/* Duplicate for infinite scroll */}\n        <span>UI/UX DESIGN</span> • <span>GRAPHIC DESIGN</span> • <span>WEB DEVELOPMENT</span> • <span>UI/UX DESIGN</span> • <span>BRANDING</span> • <span>WEBSITE DESIGN</span> • <span>DIGITAL CREATIVITY</span> • <span>E-COMMERCE SOLUTIONS</span> • <span>CUSTOM DEVELOPMENT</span>\n      </div>\n    </div>\n  );\n};\n\nexport default SkillsTicker;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,mBACEH,IAAA,QAAKI,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BH,KAAA,QAAKE,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BL,IAAA,SAAAK,QAAA,CAAM,cAAY,CAAM,CAAC,WAAG,cAAAL,IAAA,SAAAK,QAAA,CAAM,gBAAc,CAAM,CAAC,WAAG,cAAAL,IAAA,SAAAK,QAAA,CAAM,iBAAe,CAAM,CAAC,WAAG,cAAAL,IAAA,SAAAK,QAAA,CAAM,cAAY,CAAM,CAAC,WAAG,cAAAL,IAAA,SAAAK,QAAA,CAAM,UAAQ,CAAM,CAAC,WAAG,cAAAL,IAAA,SAAAK,QAAA,CAAM,gBAAc,CAAM,CAAC,WAAG,cAAAL,IAAA,SAAAK,QAAA,CAAM,oBAAkB,CAAM,CAAC,WAAG,cAAAL,IAAA,SAAAK,QAAA,CAAM,sBAAoB,CAAM,CAAC,WAAG,cAAAL,IAAA,SAAAK,QAAA,CAAM,oBAAkB,CAAM,CAAC,cAEhRL,IAAA,SAAAK,QAAA,CAAM,cAAY,CAAM,CAAC,WAAG,cAAAL,IAAA,SAAAK,QAAA,CAAM,gBAAc,CAAM,CAAC,WAAG,cAAAL,IAAA,SAAAK,QAAA,CAAM,iBAAe,CAAM,CAAC,WAAG,cAAAL,IAAA,SAAAK,QAAA,CAAM,cAAY,CAAM,CAAC,WAAG,cAAAL,IAAA,SAAAK,QAAA,CAAM,UAAQ,CAAM,CAAC,WAAG,cAAAL,IAAA,SAAAK,QAAA,CAAM,gBAAc,CAAM,CAAC,WAAG,cAAAL,IAAA,SAAAK,QAAA,CAAM,oBAAkB,CAAM,CAAC,WAAG,cAAAL,IAAA,SAAAK,QAAA,CAAM,sBAAoB,CAAM,CAAC,WAAG,cAAAL,IAAA,SAAAK,QAAA,CAAM,oBAAkB,CAAM,CAAC,EAC7Q,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}