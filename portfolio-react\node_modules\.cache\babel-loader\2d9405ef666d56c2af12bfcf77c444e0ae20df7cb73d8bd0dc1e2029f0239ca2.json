{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfulio\\\\portfolio-react\\\\src\\\\components\\\\Experience.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jobsData } from '../data/jobsData';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Experience = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"experience\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Professional Experience\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"timeline\",\n      children: jobsData.map((job, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"timeline-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"timeline-dot\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: `/job/${job.slug}`,\n          className: \"timeline-content-link\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"timeline-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: job.logo,\n              alt: job.logoAlt,\n              className: \"company-logo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 15,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"job-title\",\n              children: job.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"company-name\",\n              children: job.company\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"job-duration\",\n              children: job.duration\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"job-description\",\n              children: job.summary\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"view-details\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"View Details \\u2192\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 25,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 13\n        }, this)]\n      }, job.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = Experience;\nexport default Experience;\nvar _c;\n$RefreshReg$(_c, \"Experience\");", "map": {"version": 3, "names": ["React", "Link", "jobsData", "jsxDEV", "_jsxDEV", "Experience", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "job", "index", "to", "slug", "src", "logo", "alt", "logoAlt", "title", "company", "duration", "summary", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/Experience.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jobsData } from '../data/jobsData';\n\nconst Experience = () => {\n  return (\n    <section className=\"experience\">\n      <h2>Professional Experience</h2>\n      <div className=\"timeline\">\n        {jobsData.map((job, index) => (\n          <div key={job.id} className=\"timeline-item\">\n            <div className=\"timeline-dot\"></div>\n            <Link to={`/job/${job.slug}`} className=\"timeline-content-link\">\n              <div className=\"timeline-content\">\n                <img \n                  src={job.logo} \n                  alt={job.logoAlt} \n                  className=\"company-logo\" \n                />\n                <h3 className=\"job-title\">{job.title}</h3>\n                <h4 className=\"company-name\">{job.company}</h4>\n                <p className=\"job-duration\">{job.duration}</p>\n                <p className=\"job-description\">{job.summary}</p>\n                <div className=\"view-details\">\n                  <span>View Details →</span>\n                </div>\n              </div>\n            </Link>\n          </div>\n        ))}\n      </div>\n    </section>\n  );\n};\n\nexport default Experience;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACvB,oBACED,OAAA;IAASE,SAAS,EAAC,YAAY;IAAAC,QAAA,gBAC7BH,OAAA;MAAAG,QAAA,EAAI;IAAuB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAChCP,OAAA;MAAKE,SAAS,EAAC,UAAU;MAAAC,QAAA,EACtBL,QAAQ,CAACU,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACvBV,OAAA;QAAkBE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBACzCH,OAAA;UAAKE,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpCP,OAAA,CAACH,IAAI;UAACc,EAAE,EAAE,QAAQF,GAAG,CAACG,IAAI,EAAG;UAACV,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eAC7DH,OAAA;YAAKE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BH,OAAA;cACEa,GAAG,EAAEJ,GAAG,CAACK,IAAK;cACdC,GAAG,EAAEN,GAAG,CAACO,OAAQ;cACjBd,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACFP,OAAA;cAAIE,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEM,GAAG,CAACQ;YAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1CP,OAAA;cAAIE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEM,GAAG,CAACS;YAAO;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/CP,OAAA;cAAGE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEM,GAAG,CAACU;YAAQ;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9CP,OAAA;cAAGE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAEM,GAAG,CAACW;YAAO;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChDP,OAAA;cAAKE,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BH,OAAA;gBAAAG,QAAA,EAAM;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA,GAjBCE,GAAG,CAACY,EAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAkBX,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACe,EAAA,GA7BIrB,UAAU;AA+BhB,eAAeA,UAAU;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}