{"ast": null, "code": "import React from'react';import{Link}from'react-router-dom';import{jobsData}from'../data/jobsData';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Experience=()=>{return/*#__PURE__*/_jsxs(\"section\",{className:\"experience\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Professional Experience\"}),/*#__PURE__*/_jsx(\"div\",{className:\"timeline\",children:jobsData.map((job,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"timeline-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"timeline-dot\"}),/*#__PURE__*/_jsx(Link,{to:\"/job/\".concat(job.slug),className:\"timeline-content-link\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"timeline-content\",children:[/*#__PURE__*/_jsx(\"img\",{src:job.logo,alt:job.logoAlt,className:\"company-logo\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"job-title\",children:job.title}),/*#__PURE__*/_jsx(\"h4\",{className:\"company-name\",children:job.company}),/*#__PURE__*/_jsx(\"p\",{className:\"job-duration\",children:job.duration}),/*#__PURE__*/_jsx(\"p\",{className:\"job-description\",children:job.summary}),/*#__PURE__*/_jsx(\"div\",{className:\"view-details\",children:/*#__PURE__*/_jsx(\"span\",{children:\"View Details \\u2192\"})})]})})]},job.id))})]});};export default Experience;", "map": {"version": 3, "names": ["React", "Link", "jobsData", "jsx", "_jsx", "jsxs", "_jsxs", "Experience", "className", "children", "map", "job", "index", "to", "concat", "slug", "src", "logo", "alt", "logoAlt", "title", "company", "duration", "summary", "id"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/Experience.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jobsData } from '../data/jobsData';\n\nconst Experience = () => {\n  return (\n    <section className=\"experience\">\n      <h2>Professional Experience</h2>\n      <div className=\"timeline\">\n        {jobsData.map((job, index) => (\n          <div key={job.id} className=\"timeline-item\">\n            <div className=\"timeline-dot\"></div>\n            <Link to={`/job/${job.slug}`} className=\"timeline-content-link\">\n              <div className=\"timeline-content\">\n                <img \n                  src={job.logo} \n                  alt={job.logoAlt} \n                  className=\"company-logo\" \n                />\n                <h3 className=\"job-title\">{job.title}</h3>\n                <h4 className=\"company-name\">{job.company}</h4>\n                <p className=\"job-duration\">{job.duration}</p>\n                <p className=\"job-description\">{job.summary}</p>\n                <div className=\"view-details\">\n                  <span>View Details →</span>\n                </div>\n              </div>\n            </Link>\n          </div>\n        ))}\n      </div>\n    </section>\n  );\n};\n\nexport default Experience;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,QAAQ,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5C,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB,mBACED,KAAA,YAASE,SAAS,CAAC,YAAY,CAAAC,QAAA,eAC7BL,IAAA,OAAAK,QAAA,CAAI,yBAAuB,CAAI,CAAC,cAChCL,IAAA,QAAKI,SAAS,CAAC,UAAU,CAAAC,QAAA,CACtBP,QAAQ,CAACQ,GAAG,CAAC,CAACC,GAAG,CAAEC,KAAK,gBACvBN,KAAA,QAAkBE,SAAS,CAAC,eAAe,CAAAC,QAAA,eACzCL,IAAA,QAAKI,SAAS,CAAC,cAAc,CAAM,CAAC,cACpCJ,IAAA,CAACH,IAAI,EAACY,EAAE,SAAAC,MAAA,CAAUH,GAAG,CAACI,IAAI,CAAG,CAACP,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cAC7DH,KAAA,QAAKE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BL,IAAA,QACEY,GAAG,CAAEL,GAAG,CAACM,IAAK,CACdC,GAAG,CAAEP,GAAG,CAACQ,OAAQ,CACjBX,SAAS,CAAC,cAAc,CACzB,CAAC,cACFJ,IAAA,OAAII,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEE,GAAG,CAACS,KAAK,CAAK,CAAC,cAC1ChB,IAAA,OAAII,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAEE,GAAG,CAACU,OAAO,CAAK,CAAC,cAC/CjB,IAAA,MAAGI,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAEE,GAAG,CAACW,QAAQ,CAAI,CAAC,cAC9ClB,IAAA,MAAGI,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAEE,GAAG,CAACY,OAAO,CAAI,CAAC,cAChDnB,IAAA,QAAKI,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BL,IAAA,SAAAK,QAAA,CAAM,qBAAc,CAAM,CAAC,CACxB,CAAC,EACH,CAAC,CACF,CAAC,GAjBCE,GAAG,CAACa,EAkBT,CACN,CAAC,CACC,CAAC,EACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAAjB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}