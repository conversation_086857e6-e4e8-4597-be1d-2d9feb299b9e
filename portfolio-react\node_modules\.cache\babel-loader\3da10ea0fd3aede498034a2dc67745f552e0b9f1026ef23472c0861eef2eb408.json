{"ast": null, "code": "import React from'react';import Header from'./Header';import Intro from'./Intro';import IntroCrafting from'./IntroCrafting';import SkillsTicker from'./SkillsTicker';import Statistics from'./Statistics';import Experience from'./Experience';import Portfolio from'./Portfolio';import ClientThoughts from'./ClientThoughts';import Contact from'./Contact';import Footer from'./Footer';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Home=()=>{return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Header,{}),/*#__PURE__*/_jsx(Intro,{}),/*#__PURE__*/_jsx(IntroCrafting,{}),/*#__PURE__*/_jsx(SkillsTicker,{}),/*#__PURE__*/_jsx(Statistics,{}),/*#__PURE__*/_jsx(Experience,{}),/*#__PURE__*/_jsx(Portfolio,{}),/*#__PURE__*/_jsx(SkillsTicker,{}),/*#__PURE__*/_jsx(ClientThoughts,{}),/*#__PURE__*/_jsx(Contact,{}),/*#__PURE__*/_jsx(Footer,{})]});};export default Home;", "map": {"version": 3, "names": ["React", "Header", "Intro", "IntroCrafting", "SkillsTicker", "Statistics", "Experience", "Portfolio", "ClientThoughts", "Contact", "Footer", "jsx", "_jsx", "jsxs", "_jsxs", "Home", "children"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/Home.js"], "sourcesContent": ["import React from 'react';\nimport Header from './Header';\nimport Intro from './Intro';\nimport IntroCrafting from './IntroCrafting';\nimport SkillsTicker from './SkillsTicker';\nimport Statistics from './Statistics';\nimport Experience from './Experience';\nimport Portfolio from './Portfolio';\nimport ClientThoughts from './ClientThoughts';\nimport Contact from './Contact';\nimport Footer from './Footer';\n\nconst Home = () => {\n  return (\n    <div>\n      <Header />\n      <Intro />\n      <IntroCrafting />\n      <SkillsTicker />\n      <Statistics />\n      <Experience />\n      <Portfolio />\n      <SkillsTicker />\n      <ClientThoughts />\n      <Contact />\n      <Footer />\n    </div>\n  );\n};\n\nexport default Home;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,UAAU,CAC7B,MAAO,CAAAC,KAAK,KAAM,SAAS,CAC3B,MAAO,CAAAC,aAAa,KAAM,iBAAiB,CAC3C,MAAO,CAAAC,YAAY,KAAM,gBAAgB,CACzC,MAAO,CAAAC,UAAU,KAAM,cAAc,CACrC,MAAO,CAAAC,UAAU,KAAM,cAAc,CACrC,MAAO,CAAAC,SAAS,KAAM,aAAa,CACnC,MAAO,CAAAC,cAAc,KAAM,kBAAkB,CAC7C,MAAO,CAAAC,OAAO,KAAM,WAAW,CAC/B,MAAO,CAAAC,MAAM,KAAM,UAAU,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9B,KAAM,CAAAC,IAAI,CAAGA,CAAA,GAAM,CACjB,mBACED,KAAA,QAAAE,QAAA,eACEJ,IAAA,CAACX,MAAM,GAAE,CAAC,cACVW,IAAA,CAACV,KAAK,GAAE,CAAC,cACTU,IAAA,CAACT,aAAa,GAAE,CAAC,cACjBS,IAAA,CAACR,YAAY,GAAE,CAAC,cAChBQ,IAAA,CAACP,UAAU,GAAE,CAAC,cACdO,IAAA,CAACN,UAAU,GAAE,CAAC,cACdM,IAAA,CAACL,SAAS,GAAE,CAAC,cACbK,IAAA,CAACR,YAAY,GAAE,CAAC,cAChBQ,IAAA,CAACJ,cAAc,GAAE,CAAC,cAClBI,IAAA,CAACH,OAAO,GAAE,CAAC,cACXG,IAAA,CAACF,MAAM,GAAE,CAAC,EACP,CAAC,CAEV,CAAC,CAED,cAAe,CAAAK,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}