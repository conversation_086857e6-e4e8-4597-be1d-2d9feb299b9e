{"ast": null, "code": "import React from'react';import{BrowserRouter as Router,Routes,Route}from'react-router-dom';import'./App.css';import Home from'./components/Home';import JobDetail from'./components/JobDetail';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsx(\"div\",{className:\"App\",children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Home,{})}),/*#__PURE__*/_jsx(Route,{path:\"/job/:slug\",element:/*#__PURE__*/_jsx(JobDetail,{})})]})})});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Home", "JobDetail", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "className", "path", "element"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport './App.css';\nimport Home from './components/Home';\nimport JobDetail from './components/JobDetail';\n\nfunction App() {\n  return (\n    <Router>\n      <div className=\"App\">\n        <Routes>\n          <Route path=\"/\" element={<Home />} />\n          <Route path=\"/job/:slug\" element={<JobDetail />} />\n        </Routes>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,KAAQ,kBAAkB,CACzE,MAAO,WAAW,CAClB,MAAO,CAAAC,IAAI,KAAM,mBAAmB,CACpC,MAAO,CAAAC,SAAS,KAAM,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACEH,IAAA,CAACN,MAAM,EAAAU,QAAA,cACLJ,IAAA,QAAKK,SAAS,CAAC,KAAK,CAAAD,QAAA,cAClBF,KAAA,CAACP,MAAM,EAAAS,QAAA,eACLJ,IAAA,CAACJ,KAAK,EAACU,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEP,IAAA,CAACH,IAAI,GAAE,CAAE,CAAE,CAAC,cACrCG,IAAA,CAACJ,KAAK,EAACU,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEP,IAAA,CAACF,SAAS,GAAE,CAAE,CAAE,CAAC,EAC7C,CAAC,CACN,CAAC,CACA,CAAC,CAEb,CAEA,cAAe,CAAAK,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}