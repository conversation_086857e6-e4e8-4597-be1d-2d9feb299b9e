{"ast": null, "code": "import React from'react';import{use<PERSON><PERSON><PERSON>,<PERSON>}from'react-router-dom';import{jobsData}from'../data/jobsData';import Header from'./Header';import Footer from'./Footer';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const JobDetail=()=>{const{slug}=useParams();const job=jobsData.find(job=>job.slug===slug);if(!job){return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(<PERSON>er,{}),/*#__PURE__*/_jsxs(\"div\",{style:{padding:'100px 20px',textAlign:'center',color:'white'},children:[/*#__PURE__*/_jsx(\"h1\",{children:\"Job Not Found\"}),/*#__PURE__*/_jsx(<PERSON>,{to:\"/\",style:{color:'#4B0082'},children:\"\\u2190 Back to Home\"})]}),/*#__PURE__*/_jsx(<PERSON>er,{})]});}return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Header,{}),/*#__PURE__*/_jsx(\"div\",{className:\"back-navigation\",children:/*#__PURE__*/_jsxs(Link,{to:\"/#experience\",className:\"back-button\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"back-arrow\",children:\"\\u2190\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Back to Timeline\"})]})}),/*#__PURE__*/_jsx(\"section\",{className:\"job-hero\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"job-hero-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"company-branding\",children:[/*#__PURE__*/_jsx(\"img\",{src:job.logo,alt:job.logoAlt,className:\"hero-company-logo\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"company-info\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"job-title-hero\",children:job.title}),/*#__PURE__*/_jsx(\"h2\",{className:\"company-name-hero\",children:job.company}),/*#__PURE__*/_jsx(\"p\",{className:\"job-duration-hero\",children:job.duration})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"job-summary\",children:/*#__PURE__*/_jsx(\"p\",{children:job.summary})})]})}),/*#__PURE__*/_jsx(\"section\",{className:\"job-content\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"content-grid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"content-card\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Role Overview\"}),/*#__PURE__*/_jsx(\"p\",{children:job.roleOverview}),/*#__PURE__*/_jsx(\"h4\",{children:\"Key Responsibilities\"}),/*#__PURE__*/_jsx(\"ul\",{children:job.responsibilities.map((responsibility,index)=>/*#__PURE__*/_jsx(\"li\",{children:responsibility},index))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"content-card\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Technologies & Skills\"}),/*#__PURE__*/_jsx(\"div\",{className:\"skills-grid\",children:Object.entries(job.skills).map(_ref=>{let[category,skills]=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:\"skill-category\",children:[/*#__PURE__*/_jsx(\"h4\",{children:category}),/*#__PURE__*/_jsx(\"div\",{className:\"skill-tags\",children:skills.map((skill,index)=>/*#__PURE__*/_jsx(\"span\",{className:\"skill-tag\",children:skill},index))})]},category);})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"content-card\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Key Accomplishments\"}),/*#__PURE__*/_jsx(\"div\",{className:\"accomplishments-list\",children:job.accomplishments.map((accomplishment,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"accomplishment-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"metric\",children:accomplishment.metric}),/*#__PURE__*/_jsx(\"div\",{className:\"metric-description\",children:accomplishment.description})]},index))})]})]})}),/*#__PURE__*/_jsxs(\"section\",{className:\"role-projects\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Projects from this Role\"}),/*#__PURE__*/_jsx(\"div\",{className:\"projects-grid\",children:job.projects.map((project,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"project-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"project-image\",children:/*#__PURE__*/_jsx(\"img\",{src:project.image,alt:project.title})}),/*#__PURE__*/_jsxs(\"div\",{className:\"project-info\",children:[/*#__PURE__*/_jsx(\"h3\",{children:project.title}),/*#__PURE__*/_jsx(\"p\",{children:project.description}),/*#__PURE__*/_jsx(\"div\",{className:\"project-tech\",children:project.technologies.map((tech,techIndex)=>/*#__PURE__*/_jsx(\"span\",{children:tech},techIndex))})]})]},index))})]}),/*#__PURE__*/_jsx(Footer,{})]});};export default JobDetail;", "map": {"version": 3, "names": ["React", "useParams", "Link", "jobsData", "Header", "Footer", "jsx", "_jsx", "jsxs", "_jsxs", "JobDetail", "slug", "job", "find", "children", "style", "padding", "textAlign", "color", "to", "className", "src", "logo", "alt", "logoAlt", "title", "company", "duration", "summary", "roleOverview", "responsibilities", "map", "responsibility", "index", "Object", "entries", "skills", "_ref", "category", "skill", "accomplishments", "accomplishment", "metric", "description", "projects", "project", "image", "technologies", "tech", "techIndex"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/JobDetail.js"], "sourcesContent": ["import React from 'react';\nimport { usePara<PERSON>, Link } from 'react-router-dom';\nimport { jobsData } from '../data/jobsData';\nimport Header from './Header';\nimport Footer from './Footer';\n\nconst JobDetail = () => {\n  const { slug } = useParams();\n  const job = jobsData.find(job => job.slug === slug);\n\n  if (!job) {\n    return (\n      <div>\n        <Header />\n        <div style={{ padding: '100px 20px', textAlign: 'center', color: 'white' }}>\n          <h1>Job Not Found</h1>\n          <Link to=\"/\" style={{ color: '#4B0082' }}>← Back to Home</Link>\n        </div>\n        <Footer />\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <Header />\n      \n      {/* Navigation Back */}\n      <div className=\"back-navigation\">\n        <Link to=\"/#experience\" className=\"back-button\">\n          <span className=\"back-arrow\">←</span>\n          <span>Back to Timeline</span>\n        </Link>\n      </div>\n\n      {/* Job Detail Hero Section */}\n      <section className=\"job-hero\">\n        <div className=\"job-hero-content\">\n          <div className=\"company-branding\">\n            <img \n              src={job.logo} \n              alt={job.logoAlt} \n              className=\"hero-company-logo\" \n            />\n            <div className=\"company-info\">\n              <h1 className=\"job-title-hero\">{job.title}</h1>\n              <h2 className=\"company-name-hero\">{job.company}</h2>\n              <p className=\"job-duration-hero\">{job.duration}</p>\n            </div>\n          </div>\n          <div className=\"job-summary\">\n            <p>{job.summary}</p>\n          </div>\n        </div>\n      </section>\n\n      {/* Job Details Content */}\n      <section className=\"job-content\">\n        <div className=\"content-grid\">\n          {/* Full Job Description */}\n          <div className=\"content-card\">\n            <h3>Role Overview</h3>\n            <p>{job.roleOverview}</p>\n            \n            <h4>Key Responsibilities</h4>\n            <ul>\n              {job.responsibilities.map((responsibility, index) => (\n                <li key={index}>{responsibility}</li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Skills & Technologies */}\n          <div className=\"content-card\">\n            <h3>Technologies & Skills</h3>\n            <div className=\"skills-grid\">\n              {Object.entries(job.skills).map(([category, skills]) => (\n                <div key={category} className=\"skill-category\">\n                  <h4>{category}</h4>\n                  <div className=\"skill-tags\">\n                    {skills.map((skill, index) => (\n                      <span key={index} className=\"skill-tag\">{skill}</span>\n                    ))}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Key Accomplishments */}\n          <div className=\"content-card\">\n            <h3>Key Accomplishments</h3>\n            <div className=\"accomplishments-list\">\n              {job.accomplishments.map((accomplishment, index) => (\n                <div key={index} className=\"accomplishment-item\">\n                  <div className=\"metric\">{accomplishment.metric}</div>\n                  <div className=\"metric-description\">{accomplishment.description}</div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Project Portfolio from this role */}\n      <section className=\"role-projects\">\n        <h2>Projects from this Role</h2>\n        <div className=\"projects-grid\">\n          {job.projects.map((project, index) => (\n            <div key={index} className=\"project-card\">\n              <div className=\"project-image\">\n                <img src={project.image} alt={project.title} />\n              </div>\n              <div className=\"project-info\">\n                <h3>{project.title}</h3>\n                <p>{project.description}</p>\n                <div className=\"project-tech\">\n                  {project.technologies.map((tech, techIndex) => (\n                    <span key={techIndex}>{tech}</span>\n                  ))}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </section>\n\n      <Footer />\n    </div>\n  );\n};\n\nexport default JobDetail;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,SAAS,CAAEC,IAAI,KAAQ,kBAAkB,CAClD,OAASC,QAAQ,KAAQ,kBAAkB,CAC3C,MAAO,CAAAC,MAAM,KAAM,UAAU,CAC7B,MAAO,CAAAC,MAAM,KAAM,UAAU,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9B,KAAM,CAAAC,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAAEC,IAAK,CAAC,CAAGV,SAAS,CAAC,CAAC,CAC5B,KAAM,CAAAW,GAAG,CAAGT,QAAQ,CAACU,IAAI,CAACD,GAAG,EAAIA,GAAG,CAACD,IAAI,GAAKA,IAAI,CAAC,CAEnD,GAAI,CAACC,GAAG,CAAE,CACR,mBACEH,KAAA,QAAAK,QAAA,eACEP,IAAA,CAACH,MAAM,GAAE,CAAC,cACVK,KAAA,QAAKM,KAAK,CAAE,CAAEC,OAAO,CAAE,YAAY,CAAEC,SAAS,CAAE,QAAQ,CAAEC,KAAK,CAAE,OAAQ,CAAE,CAAAJ,QAAA,eACzEP,IAAA,OAAAO,QAAA,CAAI,eAAa,CAAI,CAAC,cACtBP,IAAA,CAACL,IAAI,EAACiB,EAAE,CAAC,GAAG,CAACJ,KAAK,CAAE,CAAEG,KAAK,CAAE,SAAU,CAAE,CAAAJ,QAAA,CAAC,qBAAc,CAAM,CAAC,EAC5D,CAAC,cACNP,IAAA,CAACF,MAAM,GAAE,CAAC,EACP,CAAC,CAEV,CAEA,mBACEI,KAAA,QAAAK,QAAA,eACEP,IAAA,CAACH,MAAM,GAAE,CAAC,cAGVG,IAAA,QAAKa,SAAS,CAAC,iBAAiB,CAAAN,QAAA,cAC9BL,KAAA,CAACP,IAAI,EAACiB,EAAE,CAAC,cAAc,CAACC,SAAS,CAAC,aAAa,CAAAN,QAAA,eAC7CP,IAAA,SAAMa,SAAS,CAAC,YAAY,CAAAN,QAAA,CAAC,QAAC,CAAM,CAAC,cACrCP,IAAA,SAAAO,QAAA,CAAM,kBAAgB,CAAM,CAAC,EACzB,CAAC,CACJ,CAAC,cAGNP,IAAA,YAASa,SAAS,CAAC,UAAU,CAAAN,QAAA,cAC3BL,KAAA,QAAKW,SAAS,CAAC,kBAAkB,CAAAN,QAAA,eAC/BL,KAAA,QAAKW,SAAS,CAAC,kBAAkB,CAAAN,QAAA,eAC/BP,IAAA,QACEc,GAAG,CAAET,GAAG,CAACU,IAAK,CACdC,GAAG,CAAEX,GAAG,CAACY,OAAQ,CACjBJ,SAAS,CAAC,mBAAmB,CAC9B,CAAC,cACFX,KAAA,QAAKW,SAAS,CAAC,cAAc,CAAAN,QAAA,eAC3BP,IAAA,OAAIa,SAAS,CAAC,gBAAgB,CAAAN,QAAA,CAAEF,GAAG,CAACa,KAAK,CAAK,CAAC,cAC/ClB,IAAA,OAAIa,SAAS,CAAC,mBAAmB,CAAAN,QAAA,CAAEF,GAAG,CAACc,OAAO,CAAK,CAAC,cACpDnB,IAAA,MAAGa,SAAS,CAAC,mBAAmB,CAAAN,QAAA,CAAEF,GAAG,CAACe,QAAQ,CAAI,CAAC,EAChD,CAAC,EACH,CAAC,cACNpB,IAAA,QAAKa,SAAS,CAAC,aAAa,CAAAN,QAAA,cAC1BP,IAAA,MAAAO,QAAA,CAAIF,GAAG,CAACgB,OAAO,CAAI,CAAC,CACjB,CAAC,EACH,CAAC,CACC,CAAC,cAGVrB,IAAA,YAASa,SAAS,CAAC,aAAa,CAAAN,QAAA,cAC9BL,KAAA,QAAKW,SAAS,CAAC,cAAc,CAAAN,QAAA,eAE3BL,KAAA,QAAKW,SAAS,CAAC,cAAc,CAAAN,QAAA,eAC3BP,IAAA,OAAAO,QAAA,CAAI,eAAa,CAAI,CAAC,cACtBP,IAAA,MAAAO,QAAA,CAAIF,GAAG,CAACiB,YAAY,CAAI,CAAC,cAEzBtB,IAAA,OAAAO,QAAA,CAAI,sBAAoB,CAAI,CAAC,cAC7BP,IAAA,OAAAO,QAAA,CACGF,GAAG,CAACkB,gBAAgB,CAACC,GAAG,CAAC,CAACC,cAAc,CAAEC,KAAK,gBAC9C1B,IAAA,OAAAO,QAAA,CAAiBkB,cAAc,EAAtBC,KAA2B,CACrC,CAAC,CACA,CAAC,EACF,CAAC,cAGNxB,KAAA,QAAKW,SAAS,CAAC,cAAc,CAAAN,QAAA,eAC3BP,IAAA,OAAAO,QAAA,CAAI,uBAAqB,CAAI,CAAC,cAC9BP,IAAA,QAAKa,SAAS,CAAC,aAAa,CAAAN,QAAA,CACzBoB,MAAM,CAACC,OAAO,CAACvB,GAAG,CAACwB,MAAM,CAAC,CAACL,GAAG,CAACM,IAAA,MAAC,CAACC,QAAQ,CAAEF,MAAM,CAAC,CAAAC,IAAA,oBACjD5B,KAAA,QAAoBW,SAAS,CAAC,gBAAgB,CAAAN,QAAA,eAC5CP,IAAA,OAAAO,QAAA,CAAKwB,QAAQ,CAAK,CAAC,cACnB/B,IAAA,QAAKa,SAAS,CAAC,YAAY,CAAAN,QAAA,CACxBsB,MAAM,CAACL,GAAG,CAAC,CAACQ,KAAK,CAAEN,KAAK,gBACvB1B,IAAA,SAAkBa,SAAS,CAAC,WAAW,CAAAN,QAAA,CAAEyB,KAAK,EAAnCN,KAA0C,CACtD,CAAC,CACC,CAAC,GANEK,QAOL,CAAC,EACP,CAAC,CACC,CAAC,EACH,CAAC,cAGN7B,KAAA,QAAKW,SAAS,CAAC,cAAc,CAAAN,QAAA,eAC3BP,IAAA,OAAAO,QAAA,CAAI,qBAAmB,CAAI,CAAC,cAC5BP,IAAA,QAAKa,SAAS,CAAC,sBAAsB,CAAAN,QAAA,CAClCF,GAAG,CAAC4B,eAAe,CAACT,GAAG,CAAC,CAACU,cAAc,CAAER,KAAK,gBAC7CxB,KAAA,QAAiBW,SAAS,CAAC,qBAAqB,CAAAN,QAAA,eAC9CP,IAAA,QAAKa,SAAS,CAAC,QAAQ,CAAAN,QAAA,CAAE2B,cAAc,CAACC,MAAM,CAAM,CAAC,cACrDnC,IAAA,QAAKa,SAAS,CAAC,oBAAoB,CAAAN,QAAA,CAAE2B,cAAc,CAACE,WAAW,CAAM,CAAC,GAF9DV,KAGL,CACN,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACC,CAAC,cAGVxB,KAAA,YAASW,SAAS,CAAC,eAAe,CAAAN,QAAA,eAChCP,IAAA,OAAAO,QAAA,CAAI,yBAAuB,CAAI,CAAC,cAChCP,IAAA,QAAKa,SAAS,CAAC,eAAe,CAAAN,QAAA,CAC3BF,GAAG,CAACgC,QAAQ,CAACb,GAAG,CAAC,CAACc,OAAO,CAAEZ,KAAK,gBAC/BxB,KAAA,QAAiBW,SAAS,CAAC,cAAc,CAAAN,QAAA,eACvCP,IAAA,QAAKa,SAAS,CAAC,eAAe,CAAAN,QAAA,cAC5BP,IAAA,QAAKc,GAAG,CAAEwB,OAAO,CAACC,KAAM,CAACvB,GAAG,CAAEsB,OAAO,CAACpB,KAAM,CAAE,CAAC,CAC5C,CAAC,cACNhB,KAAA,QAAKW,SAAS,CAAC,cAAc,CAAAN,QAAA,eAC3BP,IAAA,OAAAO,QAAA,CAAK+B,OAAO,CAACpB,KAAK,CAAK,CAAC,cACxBlB,IAAA,MAAAO,QAAA,CAAI+B,OAAO,CAACF,WAAW,CAAI,CAAC,cAC5BpC,IAAA,QAAKa,SAAS,CAAC,cAAc,CAAAN,QAAA,CAC1B+B,OAAO,CAACE,YAAY,CAAChB,GAAG,CAAC,CAACiB,IAAI,CAAEC,SAAS,gBACxC1C,IAAA,SAAAO,QAAA,CAAuBkC,IAAI,EAAhBC,SAAuB,CACnC,CAAC,CACC,CAAC,EACH,CAAC,GAZEhB,KAaL,CACN,CAAC,CACC,CAAC,EACC,CAAC,cAEV1B,IAAA,CAACF,MAAM,GAAE,CAAC,EACP,CAAC,CAEV,CAAC,CAED,cAAe,CAAAK,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}