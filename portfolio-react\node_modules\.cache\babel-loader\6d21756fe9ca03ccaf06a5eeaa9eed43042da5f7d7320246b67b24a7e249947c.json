{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfulio\\\\portfolio-react\\\\src\\\\components\\\\IntroCrafting.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst IntroCrafting = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"intro-crafting\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"crafting-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: [\"CRAFTING UNIQUE\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 7,\n          columnNumber: 28\n        }, this), \"DIGITAL EXPERIENCES\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"I specialize in delivering creative and functional solutions, combining design expertise with innovative web development to bring ideas to life.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"services\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"service-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/web-development-icon.jpg\",\n            alt: \"Icon\",\n            style: {\n              width: '50px',\n              height: '50px',\n              borderRadius: '50%',\n              objectFit: 'cover',\n              display: 'block',\n              margin: '0 auto 20px',\n              border: '2px solid #FF2D55'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"WEB DEV\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Creating responsive, modern websites tailored to meet client needs and goals.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"service-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/branding.jpeg\",\n            alt: \"Icon\",\n            style: {\n              width: '50px',\n              height: '50px',\n              borderRadius: '50%',\n              objectFit: 'cover',\n              display: 'block',\n              margin: '0 auto 20px',\n              border: '2px solid #FF2D55'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"BRANDING\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Building strong brand identities that resonate and leave a lasting impression.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"service-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/UI UX.png\",\n            alt: \"Icon\",\n            style: {\n              width: '50px',\n              height: '50px',\n              borderRadius: '50%',\n              objectFit: 'cover',\n              display: 'block',\n              margin: '0 auto 20px',\n              border: '2px solid #FF2D55'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"UI/UX DESIGN\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Enhancing usability and aesthetics through well-thought-out interface designs.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"intro-image-wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"intro-image\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"/Picsart_Pro.png\",\n          alt: \"Intro Image\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"social-links\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"https://www.linkedin.com/in/chouchane-amine-932324320/\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/icons8-linkedin-circled-64.png\",\n            alt: \"Linkedin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"https://www.instagram.com/_ami_nos_\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/icons8-instagram-64.png\",\n            alt: \"Instagram\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"https://github.com/aminos555\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/icons8-github-64.png\",\n            alt: \"GitHub\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"skills-bar\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"UI/UX DESIGN\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), \" \\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"DIGITAL CREATIVITY\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 39\n        }, this), \" \\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"E-COMMERCE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 73\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = IntroCrafting;\nexport default IntroCrafting;\nvar _c;\n$RefreshReg$(_c, \"IntroCrafting\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "IntroCrafting", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "style", "width", "height", "borderRadius", "objectFit", "display", "margin", "border", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/IntroCrafting.js"], "sourcesContent": ["import React from 'react';\n\nconst IntroCrafting = () => {\n  return (\n    <section className=\"intro-crafting\">\n      <div className=\"crafting-content\">\n        <h2>CRAFTING UNIQUE<br />DIGITAL EXPERIENCES</h2>\n        <p>I specialize in delivering creative and functional solutions, combining design expertise with innovative web development to bring ideas to life.</p>\n        <div className=\"services\">\n          <div className=\"service-item\">\n            <img \n              src=\"/web-development-icon.jpg\" \n              alt=\"Icon\" \n              style={{\n                width: '50px', \n                height: '50px', \n                borderRadius: '50%', \n                objectFit: 'cover', \n                display: 'block', \n                margin: '0 auto 20px', \n                border: '2px solid #FF2D55'\n              }}\n            />\n            <h3>WEB DEV</h3>\n            <p>Creating responsive, modern websites tailored to meet client needs and goals.</p>\n          </div>\n          <div className=\"service-item\">\n            <img \n              src=\"/branding.jpeg\" \n              alt=\"Icon\" \n              style={{\n                width: '50px', \n                height: '50px', \n                borderRadius: '50%', \n                objectFit: 'cover', \n                display: 'block', \n                margin: '0 auto 20px', \n                border: '2px solid #FF2D55'\n              }}\n            />\n            <h3>BRANDING</h3>\n            <p>Building strong brand identities that resonate and leave a lasting impression.</p>\n          </div>\n          <div className=\"service-item\">\n            <img \n              src=\"/UI UX.png\" \n              alt=\"Icon\" \n              style={{\n                width: '50px', \n                height: '50px', \n                borderRadius: '50%', \n                objectFit: 'cover', \n                display: 'block', \n                margin: '0 auto 20px', \n                border: '2px solid #FF2D55'\n              }}\n            />\n            <h3>UI/UX DESIGN</h3>\n            <p>Enhancing usability and aesthetics through well-thought-out interface designs.</p>\n          </div>\n        </div>\n      </div>\n      <div className=\"intro-image-wrapper\">\n        <div className=\"intro-image\">\n          <img src=\"/Picsart_Pro.png\" alt=\"Intro Image\" />\n        </div>\n        <div className=\"social-links\">\n          <a href=\"https://www.linkedin.com/in/chouchane-amine-932324320/\">\n            <img src=\"/icons8-linkedin-circled-64.png\" alt=\"Linkedin\" />\n          </a>\n          <a href=\"https://www.instagram.com/_ami_nos_\">\n            <img src=\"/icons8-instagram-64.png\" alt=\"Instagram\" />\n          </a>\n          <a href=\"https://github.com/aminos555\">\n            <img src=\"/icons8-github-64.png\" alt=\"GitHub\" />\n          </a>\n        </div>\n        <div className=\"skills-bar\">\n          <span>UI/UX DESIGN</span> • <span>DIGITAL CREATIVITY</span> • <span>E-COMMERCE</span>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default IntroCrafting;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAC1B,oBACED,OAAA;IAASE,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBACjCH,OAAA;MAAKE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BH,OAAA;QAAAG,QAAA,GAAI,iBAAe,eAAAH,OAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,uBAAmB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjDP,OAAA;QAAAG,QAAA,EAAG;MAAgJ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACvJP,OAAA;QAAKE,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBH,OAAA;UAAKE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BH,OAAA;YACEQ,GAAG,EAAC,2BAA2B;YAC/BC,GAAG,EAAC,MAAM;YACVC,KAAK,EAAE;cACLC,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdC,YAAY,EAAE,KAAK;cACnBC,SAAS,EAAE,OAAO;cAClBC,OAAO,EAAE,OAAO;cAChBC,MAAM,EAAE,aAAa;cACrBC,MAAM,EAAE;YACV;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFP,OAAA;YAAAG,QAAA,EAAI;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChBP,OAAA;YAAAG,QAAA,EAAG;UAA6E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,eACNP,OAAA;UAAKE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BH,OAAA;YACEQ,GAAG,EAAC,gBAAgB;YACpBC,GAAG,EAAC,MAAM;YACVC,KAAK,EAAE;cACLC,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdC,YAAY,EAAE,KAAK;cACnBC,SAAS,EAAE,OAAO;cAClBC,OAAO,EAAE,OAAO;cAChBC,MAAM,EAAE,aAAa;cACrBC,MAAM,EAAE;YACV;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFP,OAAA;YAAAG,QAAA,EAAI;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBP,OAAA;YAAAG,QAAA,EAAG;UAA8E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClF,CAAC,eACNP,OAAA;UAAKE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BH,OAAA;YACEQ,GAAG,EAAC,YAAY;YAChBC,GAAG,EAAC,MAAM;YACVC,KAAK,EAAE;cACLC,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdC,YAAY,EAAE,KAAK;cACnBC,SAAS,EAAE,OAAO;cAClBC,OAAO,EAAE,OAAO;cAChBC,MAAM,EAAE,aAAa;cACrBC,MAAM,EAAE;YACV;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFP,OAAA;YAAAG,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBP,OAAA;YAAAG,QAAA,EAAG;UAA8E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNP,OAAA;MAAKE,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCH,OAAA;QAAKE,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BH,OAAA;UAAKQ,GAAG,EAAC,kBAAkB;UAACC,GAAG,EAAC;QAAa;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BH,OAAA;UAAGkB,IAAI,EAAC,wDAAwD;UAAAf,QAAA,eAC9DH,OAAA;YAAKQ,GAAG,EAAC,iCAAiC;YAACC,GAAG,EAAC;UAAU;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACJP,OAAA;UAAGkB,IAAI,EAAC,qCAAqC;UAAAf,QAAA,eAC3CH,OAAA;YAAKQ,GAAG,EAAC,0BAA0B;YAACC,GAAG,EAAC;UAAW;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACJP,OAAA;UAAGkB,IAAI,EAAC,8BAA8B;UAAAf,QAAA,eACpCH,OAAA;YAAKQ,GAAG,EAAC,uBAAuB;YAACC,GAAG,EAAC;UAAQ;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBH,OAAA;UAAAG,QAAA,EAAM;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,YAAG,eAAAP,OAAA;UAAAG,QAAA,EAAM;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,YAAG,eAAAP,OAAA;UAAAG,QAAA,EAAM;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACY,EAAA,GAjFIlB,aAAa;AAmFnB,eAAeA,aAAa;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}