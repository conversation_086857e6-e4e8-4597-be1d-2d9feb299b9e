{"ast": null, "code": "import React from'react';import{Link}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Statistics=()=>{return/*#__PURE__*/_jsxs(\"section\",{className:\"statistics\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"STATISTICS\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"stats-grid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stat\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"9+\"}),/*#__PURE__*/_jsx(\"p\",{children:\"WEBSITES DEVELOPED\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"14\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Web DESIGN PROJECTS\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"2+\"}),/*#__PURE__*/_jsx(\"p\",{children:\"YEARS OF EXPERIENCE\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"stats-image\",children:/*#__PURE__*/_jsx(\"img\",{src:\"/business-8398066.jpg\",alt:\"Statistics Image\"})}),/*#__PURE__*/_jsx(Link,{to:\"/contact\",className:\"action-button\",children:\"LET'S TALK\"})]});};export default Statistics;", "map": {"version": 3, "names": ["React", "Link", "jsx", "_jsx", "jsxs", "_jsxs", "Statistics", "className", "children", "src", "alt", "to"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/Statistics.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Statistics = () => {\n  return (\n    <section className=\"statistics\">\n      <h2>STATISTICS</h2>\n      <div className=\"stats-grid\">\n        <div className=\"stat\">\n          <h3>9+</h3>\n          <p>WEBSITES DEVELOPED</p>\n        </div>\n        <div className=\"stat\">\n          <h3>14</h3>\n          <p>Web DESIGN PROJECTS</p>\n        </div>\n        <div className=\"stat\">\n          <h3>2+</h3>\n          <p>YEARS OF EXPERIENCE</p>\n        </div>\n      </div>\n      <div className=\"stats-image\">\n        <img src=\"/business-8398066.jpg\" alt=\"Statistics Image\" />\n      </div>\n      <Link to=\"/contact\" className=\"action-button\">LET'S TALK</Link>\n    </section>\n  );\n};\n\nexport default Statistics;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB,mBACED,KAAA,YAASE,SAAS,CAAC,YAAY,CAAAC,QAAA,eAC7BL,IAAA,OAAAK,QAAA,CAAI,YAAU,CAAI,CAAC,cACnBH,KAAA,QAAKE,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBH,KAAA,QAAKE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBL,IAAA,OAAAK,QAAA,CAAI,IAAE,CAAI,CAAC,cACXL,IAAA,MAAAK,QAAA,CAAG,oBAAkB,CAAG,CAAC,EACtB,CAAC,cACNH,KAAA,QAAKE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBL,IAAA,OAAAK,QAAA,CAAI,IAAE,CAAI,CAAC,cACXL,IAAA,MAAAK,QAAA,CAAG,qBAAmB,CAAG,CAAC,EACvB,CAAC,cACNH,KAAA,QAAKE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBL,IAAA,OAAAK,QAAA,CAAI,IAAE,CAAI,CAAC,cACXL,IAAA,MAAAK,QAAA,CAAG,qBAAmB,CAAG,CAAC,EACvB,CAAC,EACH,CAAC,cACNL,IAAA,QAAKI,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BL,IAAA,QAAKM,GAAG,CAAC,uBAAuB,CAACC,GAAG,CAAC,kBAAkB,CAAE,CAAC,CACvD,CAAC,cACNP,IAAA,CAACF,IAAI,EAACU,EAAE,CAAC,UAAU,CAACJ,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,EACxD,CAAC,CAEd,CAAC,CAED,cAAe,CAAAF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}