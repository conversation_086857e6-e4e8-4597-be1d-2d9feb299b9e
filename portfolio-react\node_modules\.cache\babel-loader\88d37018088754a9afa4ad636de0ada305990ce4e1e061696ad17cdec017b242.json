{"ast": null, "code": "export const jobsData=[{id:1,slug:\"frontend-receeto\",title:\"Frontend Developer\",company:\"Receeto\",duration:\"3/2025 - 6/2025\",logo:\"/Receeto_logo.jpg\",logoAlt:\"Receeto Logo\",summary:\"A smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights.\",roleOverview:\"Note: Due to an active (NDA) contract, I'm unable to share detailed specifics about the project. However, I can briefly describe the technical scope and my personal contributions without disclosing confidential information.\\n\\nIt's a smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights.\\n\\nKey Highlights:\\nExpense Tracking & Budgeting: Tools for monitoring transactions and setting personalized financial goals.\\n\\nSpending Analytics: Interactive category-based analysis to help users make informed decisions.\\n\\nPerformance Optimization:\\n• Lazy loading for improved speed\\n• Critical CSS and production build optimization\\n\\nTech Stack & Architecture:\\n• Angular SPA with reactive state (signals)\\n• Fully responsive design for mobile and desktop\\n• Custom financial data visualizations using charts\\n\\nThis project showcases my expertise in Angular development, performance tuning, and crafting scalable, user-centric interfaces—while respecting the confidentiality of the client's program.\",responsibilities:[\"Develop responsive web applications using Angular and modern frontend technologies\",\"Implement financial data visualizations and interactive charts\",\"Optimize application performance through lazy loading and build optimization\",\"Create expense tracking and budgeting tools with real-time data processing\",\"Build responsive interfaces for both mobile and desktop platforms\",\"Implement Angular reactive state management using signals\"],skills:{\"Frontend\":[\"Angular\",\"TypeScript\",\"RxJS\",\"Angular Signals\",\"Angular CLI\"],\"Styling\":[\"CSS3\",\"SASS/SCSS\",\"Angular Material\",\"Responsive Design\",\"Bootstrap\"],\"Tools & Testing\":[\"Git\",\"Angular CLI\",\"Webpack\",\"Lighthouse (for performance auditing)\",\"Figma\"]},accomplishments:[{metric:\"40%\",description:\"Improved application performance through lazy loading and build optimization\"},{metric:\"100%\",description:\"Responsive design compatibility across mobile and desktop platforms\"},{metric:\"NDA\",description:\"Confidential project delivered successfully while maintaining client privacy\"}],projects:[{title:\"Project 1\",description:\"NDA - details confidential\",image:\"NDA.jpg\",technologies:[\"Angular\",\"TypeScript\",\"Charts.js\"]},{title:\"Project 2\",description:\"NDA - details confidential\",image:\"NDA.jpg\",technologies:[\"Angular\",\"RxJS\",\"Angular Material\"]}]},{id:2,slug:\"uiux-frontend-developer\",title:\"UI/UX Designer & Frontend Developer\",company:\"DigitalStudio Creative\",duration:\"2022 - 2023\",logo:\"https://via.placeholder.com/120x120/FF2D55/FFFFFF?text=DS\",logoAlt:\"DigitalStudio Creative Logo\",summary:\"Designed and developed responsive websites and mobile applications. Collaborated with clients to create compelling brand identities and user experiences that increased engagement by 40%.\",roleOverview:\"At DigitalStudio Creative, I bridged the gap between design and development, creating seamless user experiences from concept to implementation. My dual role allowed me to ensure design integrity throughout the development process while maintaining optimal performance and accessibility standards.\",responsibilities:[\"Design user interfaces and experiences for web and mobile applications\",\"Conduct user research and usability testing to inform design decisions\",\"Develop responsive frontend applications using modern frameworks\",\"Collaborate with clients to understand business requirements and user needs\",\"Create and maintain design systems and component libraries\",\"Optimize applications for performance and accessibility\"],skills:{\"Design Tools\":[\"Figma\",\"Adobe XD\",\"Sketch\",\"Photoshop\",\"Illustrator\"],\"Frontend Development\":[\"React.js\",\"Vue.js\",\"JavaScript\",\"SASS/SCSS\",\"Bootstrap\"],\"UX Research\":[\"User Testing\",\"Wireframing\",\"Prototyping\",\"Analytics\",\"A/B Testing\"]},accomplishments:[{metric:\"40%\",description:\"Increase in user engagement through improved UX design\"},{metric:\"25+\",description:\"Successful client projects delivered on time and budget\"},{metric:\"95%\",description:\"Client satisfaction rate based on project feedback\"},{metric:\"3\",description:\"Design awards received for outstanding user experience\"}],projects:[{title:\"Mobile Banking Application\",description:\"Designed and developed a secure, user-friendly mobile banking app with intuitive navigation\",image:\"https://via.placeholder.com/400x250/FF2D55/FFFFFF?text=Mobile+Banking+App\",technologies:[\"React Native\",\"Figma\",\"UX Research\"]},{title:\"Interactive E-Learning Platform\",description:\"Created engaging educational interface with gamification elements and progress tracking\",image:\"https://via.placeholder.com/400x250/4B0082/FFFFFF?text=E-Learning+Platform\",technologies:[\"Vue.js\",\"SCSS\",\"Adobe XD\"]},{title:\"Complete Brand Identity System\",description:\"Developed comprehensive brand guidelines and digital assets for startup company\",image:\"https://via.placeholder.com/400x250/00CED1/FFFFFF?text=Brand+Identity+System\",technologies:[\"Illustrator\",\"Photoshop\",\"Brand Strategy\"]}]},{id:3,slug:\"junior-web-developer\",title:\"Junior Web Developer\",company:\"WebDev Agency\",duration:\"2021 - 2022\",logo:\"https://via.placeholder.com/120x120/00CED1/FFFFFF?text=WD\",logoAlt:\"WebDev Agency Logo\",summary:\"Developed custom WordPress themes and e-commerce solutions. Gained expertise in HTML, CSS, JavaScript, and PHP while working on diverse client projects ranging from small businesses to enterprise solutions.\",roleOverview:\"As a Junior Web Developer at WebDev Agency, I focused on building custom websites and e-commerce solutions for a diverse range of clients. This role provided me with a solid foundation in web development fundamentals and client communication skills.\",responsibilities:[\"Develop custom WordPress themes and plugins\",\"Build responsive websites using HTML, CSS, and JavaScript\",\"Create e-commerce solutions using WooCommerce and Shopify\",\"Collaborate with designers to implement pixel-perfect designs\",\"Optimize websites for performance and SEO\",\"Provide technical support and maintenance for client websites\"],skills:{\"Frontend\":[\"HTML5\",\"CSS3\",\"JavaScript\",\"jQuery\",\"Bootstrap\"],\"Backend\":[\"PHP\",\"MySQL\",\"WordPress\",\"WooCommerce\"],\"Tools\":[\"Git\",\"Photoshop\",\"Chrome DevTools\",\"FTP\",\"cPanel\"]},accomplishments:[{metric:\"30+\",description:\"Websites successfully developed and launched\"},{metric:\"50%\",description:\"Improvement in page load speeds through optimization\"},{metric:\"100%\",description:\"Client satisfaction rate for delivered projects\"}],projects:[{title:\"Restaurant Chain Website\",description:\"Built a multi-location restaurant website with online ordering system\",image:\"https://via.placeholder.com/400x250/00CED1/FFFFFF?text=Restaurant+Website\",technologies:[\"WordPress\",\"WooCommerce\",\"PHP\"]},{title:\"Real Estate Portal\",description:\"Developed property listing website with advanced search functionality\",image:\"https://via.placeholder.com/400x250/32CD32/FFFFFF?text=Real+Estate+Portal\",technologies:[\"HTML\",\"CSS\",\"JavaScript\",\"PHP\"]}]},{id:4,slug:\"freelance-designer\",title:\"Freelance Designer\",company:\"Self-Employed\",duration:\"2020 - 2021\",logo:\"https://via.placeholder.com/120x120/32CD32/FFFFFF?text=FL\",logoAlt:\"Freelance Logo\",summary:\"Started my journey as a freelance graphic designer, creating logos, branding materials, and marketing collateral for local businesses. Built a strong foundation in design principles and client communication.\",roleOverview:\"Beginning my career as a freelance designer, I worked with local businesses to create compelling visual identities and marketing materials. This experience taught me the importance of understanding client needs and translating business objectives into effective design solutions.\",responsibilities:[\"Design logos and brand identities for small businesses\",\"Create marketing materials including flyers, brochures, and business cards\",\"Develop social media graphics and digital marketing assets\",\"Collaborate directly with business owners to understand their vision\",\"Manage multiple projects simultaneously while meeting deadlines\",\"Handle client communications and project billing\"],skills:{\"Design Software\":[\"Adobe Illustrator\",\"Adobe Photoshop\",\"Adobe InDesign\",\"Canva\"],\"Design Skills\":[\"Logo Design\",\"Brand Identity\",\"Print Design\",\"Digital Graphics\"],\"Business Skills\":[\"Client Communication\",\"Project Management\",\"Time Management\",\"Pricing\"]},accomplishments:[{metric:\"20+\",description:\"Local businesses served with design solutions\"},{metric:\"4.9/5\",description:\"Average client rating on freelance platforms\"},{metric:\"90%\",description:\"Client retention rate for ongoing projects\"}],projects:[{title:\"Local Coffee Shop Branding\",description:\"Complete brand identity including logo, menu design, and signage\",image:\"https://via.placeholder.com/400x250/32CD32/FFFFFF?text=Coffee+Shop+Brand\",technologies:[\"Illustrator\",\"Photoshop\",\"InDesign\"]},{title:\"Fitness Studio Marketing Kit\",description:\"Comprehensive marketing materials for new fitness studio launch\",image:\"https://via.placeholder.com/400x250/FF6347/FFFFFF?text=Fitness+Marketing\",technologies:[\"Photoshop\",\"Illustrator\",\"Print Design\"]}]}];", "map": {"version": 3, "names": ["jobsData", "id", "slug", "title", "company", "duration", "logo", "logoAlt", "summary", "roleOverview", "responsibilities", "skills", "accomplishments", "metric", "description", "projects", "image", "technologies"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/data/jobsData.js"], "sourcesContent": ["export const jobsData = [\n  {\n    id: 1,\n    slug: \"frontend-receeto\",\n    title: \"Frontend Developer\",\n    company: \"Receeto\",\n    duration: \"3/2025 - 6/2025\",\n    logo: \"/Receeto_logo.jpg\",\n    logoAlt: \"Receeto Logo\",\n    summary: \"A smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights.\",\n    roleOverview: \"Note: Due to an active (NDA) contract, I'm unable to share detailed specifics about the project. However, I can briefly describe the technical scope and my personal contributions without disclosing confidential information.\\n\\nIt's a smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights.\\n\\nKey Highlights:\\nExpense Tracking & Budgeting: Tools for monitoring transactions and setting personalized financial goals.\\n\\nSpending Analytics: Interactive category-based analysis to help users make informed decisions.\\n\\nPerformance Optimization:\\n• Lazy loading for improved speed\\n• Critical CSS and production build optimization\\n\\nTech Stack & Architecture:\\n• Angular SPA with reactive state (signals)\\n• Fully responsive design for mobile and desktop\\n• Custom financial data visualizations using charts\\n\\nThis project showcases my expertise in Angular development, performance tuning, and crafting scalable, user-centric interfaces—while respecting the confidentiality of the client's program.\",\n    responsibilities: [\n      \"Develop responsive web applications using Angular and modern frontend technologies\",\n      \"Implement financial data visualizations and interactive charts\",\n      \"Optimize application performance through lazy loading and build optimization\",\n      \"Create expense tracking and budgeting tools with real-time data processing\",\n      \"Build responsive interfaces for both mobile and desktop platforms\",\n      \"Implement Angular reactive state management using signals\"\n    ],\n    skills: {\n      \"Frontend\": [\"Angular\", \"TypeScript\", \"RxJS\", \"Angular Signals\", \"Angular CLI\"],\n      \"Styling\": [\"CSS3\", \"SASS/SCSS\", \"Angular Material\", \"Responsive Design\", \"Bootstrap\"],\n      \"Tools & Testing\": [\"Git\", \"Angular CLI\", \"Webpack\", \"Lighthouse (for performance auditing)\", \"Figma\"]\n    },\n    accomplishments: [\n      {\n        metric: \"40%\",\n        description: \"Improved application performance through lazy loading and build optimization\"\n      },\n      {\n        metric: \"100%\",\n        description: \"Responsive design compatibility across mobile and desktop platforms\"\n      },\n      {\n        metric: \"NDA\",\n        description: \"Confidential project delivered successfully while maintaining client privacy\"\n      }\n    ],\n    projects: [\n      {\n        title: \"Project 1\",\n        description: \"NDA - details confidential\",\n        image: \"NDA.jpg\",\n        technologies: [\"Angular\", \"TypeScript\", \"Charts.js\"]\n      },\n      {\n        title: \"Project 2\",\n        description: \"NDA - details confidential\",\n        image: \"NDA.jpg\",\n        technologies: [\"Angular\", \"RxJS\", \"Angular Material\"]\n      }\n    ]\n  },\n  {\n    id: 2,\n    slug: \"uiux-frontend-developer\",\n    title: \"UI/UX Designer & Frontend Developer\",\n    company: \"DigitalStudio Creative\",\n    duration: \"2022 - 2023\",\n    logo: \"https://via.placeholder.com/120x120/FF2D55/FFFFFF?text=DS\",\n    logoAlt: \"DigitalStudio Creative Logo\",\n    summary: \"Designed and developed responsive websites and mobile applications. Collaborated with clients to create compelling brand identities and user experiences that increased engagement by 40%.\",\n    roleOverview: \"At DigitalStudio Creative, I bridged the gap between design and development, creating seamless user experiences from concept to implementation. My dual role allowed me to ensure design integrity throughout the development process while maintaining optimal performance and accessibility standards.\",\n    responsibilities: [\n      \"Design user interfaces and experiences for web and mobile applications\",\n      \"Conduct user research and usability testing to inform design decisions\",\n      \"Develop responsive frontend applications using modern frameworks\",\n      \"Collaborate with clients to understand business requirements and user needs\",\n      \"Create and maintain design systems and component libraries\",\n      \"Optimize applications for performance and accessibility\"\n    ],\n    skills: {\n      \"Design Tools\": [\"Figma\", \"Adobe XD\", \"Sketch\", \"Photoshop\", \"Illustrator\"],\n      \"Frontend Development\": [\"React.js\", \"Vue.js\", \"JavaScript\", \"SASS/SCSS\", \"Bootstrap\"],\n      \"UX Research\": [\"User Testing\", \"Wireframing\", \"Prototyping\", \"Analytics\", \"A/B Testing\"]\n    },\n    accomplishments: [\n      {\n        metric: \"40%\",\n        description: \"Increase in user engagement through improved UX design\"\n      },\n      {\n        metric: \"25+\",\n        description: \"Successful client projects delivered on time and budget\"\n      },\n      {\n        metric: \"95%\",\n        description: \"Client satisfaction rate based on project feedback\"\n      },\n      {\n        metric: \"3\",\n        description: \"Design awards received for outstanding user experience\"\n      }\n    ],\n    projects: [\n      {\n        title: \"Mobile Banking Application\",\n        description: \"Designed and developed a secure, user-friendly mobile banking app with intuitive navigation\",\n        image: \"https://via.placeholder.com/400x250/FF2D55/FFFFFF?text=Mobile+Banking+App\",\n        technologies: [\"React Native\", \"Figma\", \"UX Research\"]\n      },\n      {\n        title: \"Interactive E-Learning Platform\",\n        description: \"Created engaging educational interface with gamification elements and progress tracking\",\n        image: \"https://via.placeholder.com/400x250/4B0082/FFFFFF?text=E-Learning+Platform\",\n        technologies: [\"Vue.js\", \"SCSS\", \"Adobe XD\"]\n      },\n      {\n        title: \"Complete Brand Identity System\",\n        description: \"Developed comprehensive brand guidelines and digital assets for startup company\",\n        image: \"https://via.placeholder.com/400x250/00CED1/FFFFFF?text=Brand+Identity+System\",\n        technologies: [\"Illustrator\", \"Photoshop\", \"Brand Strategy\"]\n      }\n    ]\n  },\n  {\n    id: 3,\n    slug: \"junior-web-developer\",\n    title: \"Junior Web Developer\",\n    company: \"WebDev Agency\",\n    duration: \"2021 - 2022\",\n    logo: \"https://via.placeholder.com/120x120/00CED1/FFFFFF?text=WD\",\n    logoAlt: \"WebDev Agency Logo\",\n    summary: \"Developed custom WordPress themes and e-commerce solutions. Gained expertise in HTML, CSS, JavaScript, and PHP while working on diverse client projects ranging from small businesses to enterprise solutions.\",\n    roleOverview: \"As a Junior Web Developer at WebDev Agency, I focused on building custom websites and e-commerce solutions for a diverse range of clients. This role provided me with a solid foundation in web development fundamentals and client communication skills.\",\n    responsibilities: [\n      \"Develop custom WordPress themes and plugins\",\n      \"Build responsive websites using HTML, CSS, and JavaScript\",\n      \"Create e-commerce solutions using WooCommerce and Shopify\",\n      \"Collaborate with designers to implement pixel-perfect designs\",\n      \"Optimize websites for performance and SEO\",\n      \"Provide technical support and maintenance for client websites\"\n    ],\n    skills: {\n      \"Frontend\": [\"HTML5\", \"CSS3\", \"JavaScript\", \"jQuery\", \"Bootstrap\"],\n      \"Backend\": [\"PHP\", \"MySQL\", \"WordPress\", \"WooCommerce\"],\n      \"Tools\": [\"Git\", \"Photoshop\", \"Chrome DevTools\", \"FTP\", \"cPanel\"]\n    },\n    accomplishments: [\n      {\n        metric: \"30+\",\n        description: \"Websites successfully developed and launched\"\n      },\n      {\n        metric: \"50%\",\n        description: \"Improvement in page load speeds through optimization\"\n      },\n      {\n        metric: \"100%\",\n        description: \"Client satisfaction rate for delivered projects\"\n      }\n    ],\n    projects: [\n      {\n        title: \"Restaurant Chain Website\",\n        description: \"Built a multi-location restaurant website with online ordering system\",\n        image: \"https://via.placeholder.com/400x250/00CED1/FFFFFF?text=Restaurant+Website\",\n        technologies: [\"WordPress\", \"WooCommerce\", \"PHP\"]\n      },\n      {\n        title: \"Real Estate Portal\",\n        description: \"Developed property listing website with advanced search functionality\",\n        image: \"https://via.placeholder.com/400x250/32CD32/FFFFFF?text=Real+Estate+Portal\",\n        technologies: [\"HTML\", \"CSS\", \"JavaScript\", \"PHP\"]\n      }\n    ]\n  },\n  {\n    id: 4,\n    slug: \"freelance-designer\",\n    title: \"Freelance Designer\",\n    company: \"Self-Employed\",\n    duration: \"2020 - 2021\",\n    logo: \"https://via.placeholder.com/120x120/32CD32/FFFFFF?text=FL\",\n    logoAlt: \"Freelance Logo\",\n    summary: \"Started my journey as a freelance graphic designer, creating logos, branding materials, and marketing collateral for local businesses. Built a strong foundation in design principles and client communication.\",\n    roleOverview: \"Beginning my career as a freelance designer, I worked with local businesses to create compelling visual identities and marketing materials. This experience taught me the importance of understanding client needs and translating business objectives into effective design solutions.\",\n    responsibilities: [\n      \"Design logos and brand identities for small businesses\",\n      \"Create marketing materials including flyers, brochures, and business cards\",\n      \"Develop social media graphics and digital marketing assets\",\n      \"Collaborate directly with business owners to understand their vision\",\n      \"Manage multiple projects simultaneously while meeting deadlines\",\n      \"Handle client communications and project billing\"\n    ],\n    skills: {\n      \"Design Software\": [\"Adobe Illustrator\", \"Adobe Photoshop\", \"Adobe InDesign\", \"Canva\"],\n      \"Design Skills\": [\"Logo Design\", \"Brand Identity\", \"Print Design\", \"Digital Graphics\"],\n      \"Business Skills\": [\"Client Communication\", \"Project Management\", \"Time Management\", \"Pricing\"]\n    },\n    accomplishments: [\n      {\n        metric: \"20+\",\n        description: \"Local businesses served with design solutions\"\n      },\n      {\n        metric: \"4.9/5\",\n        description: \"Average client rating on freelance platforms\"\n      },\n      {\n        metric: \"90%\",\n        description: \"Client retention rate for ongoing projects\"\n      }\n    ],\n    projects: [\n      {\n        title: \"Local Coffee Shop Branding\",\n        description: \"Complete brand identity including logo, menu design, and signage\",\n        image: \"https://via.placeholder.com/400x250/32CD32/FFFFFF?text=Coffee+Shop+Brand\",\n        technologies: [\"Illustrator\", \"Photoshop\", \"InDesign\"]\n      },\n      {\n        title: \"Fitness Studio Marketing Kit\",\n        description: \"Comprehensive marketing materials for new fitness studio launch\",\n        image: \"https://via.placeholder.com/400x250/FF6347/FFFFFF?text=Fitness+Marketing\",\n        technologies: [\"Photoshop\", \"Illustrator\", \"Print Design\"]\n      }\n    ]\n  }\n];\n"], "mappings": "AAAA,MAAO,MAAM,CAAAA,QAAQ,CAAG,CACtB,CACEC,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,kBAAkB,CACxBC,KAAK,CAAE,oBAAoB,CAC3BC,OAAO,CAAE,SAAS,CAClBC,QAAQ,CAAE,iBAAiB,CAC3BC,IAAI,CAAE,mBAAmB,CACzBC,OAAO,CAAE,cAAc,CACvBC,OAAO,CAAE,gJAAgJ,CACzJC,YAAY,CAAE,4jCAA4jC,CAC1kCC,gBAAgB,CAAE,CAChB,oFAAoF,CACpF,gEAAgE,CAChE,8EAA8E,CAC9E,4EAA4E,CAC5E,mEAAmE,CACnE,2DAA2D,CAC5D,CACDC,MAAM,CAAE,CACN,UAAU,CAAE,CAAC,SAAS,CAAE,YAAY,CAAE,MAAM,CAAE,iBAAiB,CAAE,aAAa,CAAC,CAC/E,SAAS,CAAE,CAAC,MAAM,CAAE,WAAW,CAAE,kBAAkB,CAAE,mBAAmB,CAAE,WAAW,CAAC,CACtF,iBAAiB,CAAE,CAAC,KAAK,CAAE,aAAa,CAAE,SAAS,CAAE,uCAAuC,CAAE,OAAO,CACvG,CAAC,CACDC,eAAe,CAAE,CACf,CACEC,MAAM,CAAE,KAAK,CACbC,WAAW,CAAE,8EACf,CAAC,CACD,CACED,MAAM,CAAE,MAAM,CACdC,WAAW,CAAE,qEACf,CAAC,CACD,CACED,MAAM,CAAE,KAAK,CACbC,WAAW,CAAE,8EACf,CAAC,CACF,CACDC,QAAQ,CAAE,CACR,CACEZ,KAAK,CAAE,WAAW,CAClBW,WAAW,CAAE,4BAA4B,CACzCE,KAAK,CAAE,SAAS,CAChBC,YAAY,CAAE,CAAC,SAAS,CAAE,YAAY,CAAE,WAAW,CACrD,CAAC,CACD,CACEd,KAAK,CAAE,WAAW,CAClBW,WAAW,CAAE,4BAA4B,CACzCE,KAAK,CAAE,SAAS,CAChBC,YAAY,CAAE,CAAC,SAAS,CAAE,MAAM,CAAE,kBAAkB,CACtD,CAAC,CAEL,CAAC,CACD,CACEhB,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,yBAAyB,CAC/BC,KAAK,CAAE,qCAAqC,CAC5CC,OAAO,CAAE,wBAAwB,CACjCC,QAAQ,CAAE,aAAa,CACvBC,IAAI,CAAE,2DAA2D,CACjEC,OAAO,CAAE,6BAA6B,CACtCC,OAAO,CAAE,4LAA4L,CACrMC,YAAY,CAAE,0SAA0S,CACxTC,gBAAgB,CAAE,CAChB,wEAAwE,CACxE,wEAAwE,CACxE,kEAAkE,CAClE,6EAA6E,CAC7E,4DAA4D,CAC5D,yDAAyD,CAC1D,CACDC,MAAM,CAAE,CACN,cAAc,CAAE,CAAC,OAAO,CAAE,UAAU,CAAE,QAAQ,CAAE,WAAW,CAAE,aAAa,CAAC,CAC3E,sBAAsB,CAAE,CAAC,UAAU,CAAE,QAAQ,CAAE,YAAY,CAAE,WAAW,CAAE,WAAW,CAAC,CACtF,aAAa,CAAE,CAAC,cAAc,CAAE,aAAa,CAAE,aAAa,CAAE,WAAW,CAAE,aAAa,CAC1F,CAAC,CACDC,eAAe,CAAE,CACf,CACEC,MAAM,CAAE,KAAK,CACbC,WAAW,CAAE,wDACf,CAAC,CACD,CACED,MAAM,CAAE,KAAK,CACbC,WAAW,CAAE,yDACf,CAAC,CACD,CACED,MAAM,CAAE,KAAK,CACbC,WAAW,CAAE,oDACf,CAAC,CACD,CACED,MAAM,CAAE,GAAG,CACXC,WAAW,CAAE,wDACf,CAAC,CACF,CACDC,QAAQ,CAAE,CACR,CACEZ,KAAK,CAAE,4BAA4B,CACnCW,WAAW,CAAE,6FAA6F,CAC1GE,KAAK,CAAE,2EAA2E,CAClFC,YAAY,CAAE,CAAC,cAAc,CAAE,OAAO,CAAE,aAAa,CACvD,CAAC,CACD,CACEd,KAAK,CAAE,iCAAiC,CACxCW,WAAW,CAAE,yFAAyF,CACtGE,KAAK,CAAE,4EAA4E,CACnFC,YAAY,CAAE,CAAC,QAAQ,CAAE,MAAM,CAAE,UAAU,CAC7C,CAAC,CACD,CACEd,KAAK,CAAE,gCAAgC,CACvCW,WAAW,CAAE,iFAAiF,CAC9FE,KAAK,CAAE,8EAA8E,CACrFC,YAAY,CAAE,CAAC,aAAa,CAAE,WAAW,CAAE,gBAAgB,CAC7D,CAAC,CAEL,CAAC,CACD,CACEhB,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,sBAAsB,CAC5BC,KAAK,CAAE,sBAAsB,CAC7BC,OAAO,CAAE,eAAe,CACxBC,QAAQ,CAAE,aAAa,CACvBC,IAAI,CAAE,2DAA2D,CACjEC,OAAO,CAAE,oBAAoB,CAC7BC,OAAO,CAAE,gNAAgN,CACzNC,YAAY,CAAE,2PAA2P,CACzQC,gBAAgB,CAAE,CAChB,6CAA6C,CAC7C,2DAA2D,CAC3D,2DAA2D,CAC3D,+DAA+D,CAC/D,2CAA2C,CAC3C,+DAA+D,CAChE,CACDC,MAAM,CAAE,CACN,UAAU,CAAE,CAAC,OAAO,CAAE,MAAM,CAAE,YAAY,CAAE,QAAQ,CAAE,WAAW,CAAC,CAClE,SAAS,CAAE,CAAC,KAAK,CAAE,OAAO,CAAE,WAAW,CAAE,aAAa,CAAC,CACvD,OAAO,CAAE,CAAC,KAAK,CAAE,WAAW,CAAE,iBAAiB,CAAE,KAAK,CAAE,QAAQ,CAClE,CAAC,CACDC,eAAe,CAAE,CACf,CACEC,MAAM,CAAE,KAAK,CACbC,WAAW,CAAE,8CACf,CAAC,CACD,CACED,MAAM,CAAE,KAAK,CACbC,WAAW,CAAE,sDACf,CAAC,CACD,CACED,MAAM,CAAE,MAAM,CACdC,WAAW,CAAE,iDACf,CAAC,CACF,CACDC,QAAQ,CAAE,CACR,CACEZ,KAAK,CAAE,0BAA0B,CACjCW,WAAW,CAAE,uEAAuE,CACpFE,KAAK,CAAE,2EAA2E,CAClFC,YAAY,CAAE,CAAC,WAAW,CAAE,aAAa,CAAE,KAAK,CAClD,CAAC,CACD,CACEd,KAAK,CAAE,oBAAoB,CAC3BW,WAAW,CAAE,uEAAuE,CACpFE,KAAK,CAAE,2EAA2E,CAClFC,YAAY,CAAE,CAAC,MAAM,CAAE,KAAK,CAAE,YAAY,CAAE,KAAK,CACnD,CAAC,CAEL,CAAC,CACD,CACEhB,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,oBAAoB,CAC1BC,KAAK,CAAE,oBAAoB,CAC3BC,OAAO,CAAE,eAAe,CACxBC,QAAQ,CAAE,aAAa,CACvBC,IAAI,CAAE,2DAA2D,CACjEC,OAAO,CAAE,gBAAgB,CACzBC,OAAO,CAAE,iNAAiN,CAC1NC,YAAY,CAAE,yRAAyR,CACvSC,gBAAgB,CAAE,CAChB,wDAAwD,CACxD,4EAA4E,CAC5E,4DAA4D,CAC5D,sEAAsE,CACtE,iEAAiE,CACjE,kDAAkD,CACnD,CACDC,MAAM,CAAE,CACN,iBAAiB,CAAE,CAAC,mBAAmB,CAAE,iBAAiB,CAAE,gBAAgB,CAAE,OAAO,CAAC,CACtF,eAAe,CAAE,CAAC,aAAa,CAAE,gBAAgB,CAAE,cAAc,CAAE,kBAAkB,CAAC,CACtF,iBAAiB,CAAE,CAAC,sBAAsB,CAAE,oBAAoB,CAAE,iBAAiB,CAAE,SAAS,CAChG,CAAC,CACDC,eAAe,CAAE,CACf,CACEC,MAAM,CAAE,KAAK,CACbC,WAAW,CAAE,+CACf,CAAC,CACD,CACED,MAAM,CAAE,OAAO,CACfC,WAAW,CAAE,8CACf,CAAC,CACD,CACED,MAAM,CAAE,KAAK,CACbC,WAAW,CAAE,4CACf,CAAC,CACF,CACDC,QAAQ,CAAE,CACR,CACEZ,KAAK,CAAE,4BAA4B,CACnCW,WAAW,CAAE,kEAAkE,CAC/EE,KAAK,CAAE,0EAA0E,CACjFC,YAAY,CAAE,CAAC,aAAa,CAAE,WAAW,CAAE,UAAU,CACvD,CAAC,CACD,CACEd,KAAK,CAAE,8BAA8B,CACrCW,WAAW,CAAE,iEAAiE,CAC9EE,KAAK,CAAE,0EAA0E,CACjFC,YAAY,CAAE,CAAC,WAAW,CAAE,aAAa,CAAE,cAAc,CAC3D,CAAC,CAEL,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}