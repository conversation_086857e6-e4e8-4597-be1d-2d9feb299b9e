{"ast": null, "code": "import React,{useEffect,useRef}from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Portfolio=()=>{const carouselTrackRef=useRef(null);useEffect(()=>{const carouselTrack=carouselTrackRef.current;if(!carouselTrack)return;let isDragging=false;let isHovering=false;let startX;let startScrollLeft;let autoScrollInterval;let scrollSpeed=1;// Auto-scroll function\nconst startAutoScroll=()=>{if(autoScrollInterval)clearInterval(autoScrollInterval);autoScrollInterval=setInterval(()=>{if(!isDragging&&!isHovering){carouselTrack.scrollLeft+=scrollSpeed;const trackWidth=carouselTrack.scrollWidth/2;if(carouselTrack.scrollLeft>=trackWidth){carouselTrack.scrollLeft=0;}}},16);};const stopAutoScroll=()=>{if(autoScrollInterval){clearInterval(autoScrollInterval);autoScrollInterval=null;}};// Mouse events\nconst handleMouseEnter=()=>{isHovering=true;};const handleMouseLeave=()=>{isHovering=false;};const handleMouseDown=e=>{isDragging=true;isHovering=true;carouselTrack.classList.add('dragging');startX=e.pageX;startScrollLeft=carouselTrack.scrollLeft;};const handleMouseMove=e=>{if(!isDragging)return;const x=e.pageX;const walk=(x-startX)*1.8;carouselTrack.scrollLeft=startScrollLeft-walk;const trackWidth=carouselTrack.scrollWidth/2;if(carouselTrack.scrollLeft>=trackWidth){carouselTrack.scrollLeft=0;}else if(carouselTrack.scrollLeft<0){carouselTrack.scrollLeft=trackWidth-1;}};const handleMouseUp=()=>{if(isDragging){isDragging=false;carouselTrack.classList.remove('dragging');setTimeout(()=>{if(!isHovering){// Auto-scroll will resume\n}},100);}};const handleWheel=e=>{e.preventDefault();const wheelDelta=e.deltaY;const scrollAmount=wheelDelta>0?50:-50;carouselTrack.scrollLeft+=scrollAmount;const trackWidth=carouselTrack.scrollWidth/2;if(carouselTrack.scrollLeft>=trackWidth){carouselTrack.scrollLeft=0;}else if(carouselTrack.scrollLeft<0){carouselTrack.scrollLeft=trackWidth-1;}};// Add event listeners\ncarouselTrack.addEventListener('mouseenter',handleMouseEnter);carouselTrack.addEventListener('mouseleave',handleMouseLeave);carouselTrack.addEventListener('mousedown',handleMouseDown);carouselTrack.addEventListener('mousemove',handleMouseMove);document.addEventListener('mouseup',handleMouseUp);carouselTrack.addEventListener('wheel',handleWheel,{passive:false});// Start auto-scroll\nstartAutoScroll();// Cleanup\nreturn()=>{stopAutoScroll();carouselTrack.removeEventListener('mouseenter',handleMouseEnter);carouselTrack.removeEventListener('mouseleave',handleMouseLeave);carouselTrack.removeEventListener('mousedown',handleMouseDown);carouselTrack.removeEventListener('mousemove',handleMouseMove);document.removeEventListener('mouseup',handleMouseUp);carouselTrack.removeEventListener('wheel',handleWheel);};},[]);const portfolioItems=[{href:\"https://threed-e-commerce.onrender.com\",image:\"/3D E-Comm.PNG\",alt:\"3D Ecommerce\",title:\"3D Ecommerce (Finish Soon)\"},{href:\"#\",image:\"/ex1.webp\",alt:\"Yalla Go Posters\",title:\"Will be deployed soon.\"},{href:\"#\",image:\"/ex2.png\",alt:\"Nexit Brand Identity\",title:\"Will be deployed soon.\"},{href:\"#\",image:\"/ex3.webp\",alt:\"Yalla Go Posters\",title:\"Will be deployed soon.\"},{href:\"#\",image:\"/ex4.1.png\",alt:\"Yalla Go Posters\",title:\"Will be deployed soon.\"},{href:\"#\",image:\"/ex5.png\",alt:\"Yalla Go Posters\",title:\"Will be deployed soon.\"},{href:\"#\",image:\"/bussniss web UI.PNG\",alt:\"Business Web UI\",title:\"Available in git Will be deployed soon.\"}];return/*#__PURE__*/_jsxs(\"section\",{className:\"portfolio\",children:[/*#__PURE__*/_jsxs(\"h2\",{children:[\"Top Projects\",/*#__PURE__*/_jsx(\"br\",{})]}),/*#__PURE__*/_jsx(\"a\",{href:\"#\",className:\"discover-button\",children:\"DISCOVER MORE\"}),/*#__PURE__*/_jsx(\"div\",{className:\"portfolio-carousel\",children:/*#__PURE__*/_jsx(\"div\",{className:\"carousel-track\",ref:carouselTrackRef,children:[...portfolioItems,...portfolioItems].map((item,index)=>/*#__PURE__*/_jsx(\"div\",{className:\"portfolio-item\",children:/*#__PURE__*/_jsxs(\"a\",{href:item.href,target:\"_blank\",rel:\"noopener noreferrer\",children:[/*#__PURE__*/_jsx(\"img\",{src:item.image,alt:item.alt}),/*#__PURE__*/_jsx(\"p\",{children:item.title})]})},index))})})]});};export default Portfolio;", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "jsx", "_jsx", "jsxs", "_jsxs", "Portfolio", "carouselTrackRef", "carouselTrack", "current", "isDragging", "isHovering", "startX", "startScrollLeft", "autoScrollInterval", "scrollSpeed", "startAutoScroll", "clearInterval", "setInterval", "scrollLeft", "trackWidth", "scrollWidth", "stopAutoScroll", "handleMouseEnter", "handleMouseLeave", "handleMouseDown", "e", "classList", "add", "pageX", "handleMouseMove", "x", "walk", "handleMouseUp", "remove", "setTimeout", "handleWheel", "preventDefault", "wheelDelta", "deltaY", "scrollAmount", "addEventListener", "document", "passive", "removeEventListener", "portfolioItems", "href", "image", "alt", "title", "className", "children", "ref", "map", "item", "index", "target", "rel", "src"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/Portfolio.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\n\nconst Portfolio = () => {\n  const carouselTrackRef = useRef(null);\n\n  useEffect(() => {\n    const carouselTrack = carouselTrackRef.current;\n    if (!carouselTrack) return;\n\n    let isDragging = false;\n    let isHovering = false;\n    let startX;\n    let startScrollLeft;\n    let autoScrollInterval;\n    let scrollSpeed = 1;\n\n    // Auto-scroll function\n    const startAutoScroll = () => {\n      if (autoScrollInterval) clearInterval(autoScrollInterval);\n      autoScrollInterval = setInterval(() => {\n        if (!isDragging && !isHovering) {\n          carouselTrack.scrollLeft += scrollSpeed;\n          const trackWidth = carouselTrack.scrollWidth / 2;\n          if (carouselTrack.scrollLeft >= trackWidth) {\n            carouselTrack.scrollLeft = 0;\n          }\n        }\n      }, 16);\n    };\n\n    const stopAutoScroll = () => {\n      if (autoScrollInterval) {\n        clearInterval(autoScrollInterval);\n        autoScrollInterval = null;\n      }\n    };\n\n    // Mouse events\n    const handleMouseEnter = () => {\n      isHovering = true;\n    };\n\n    const handleMouseLeave = () => {\n      isHovering = false;\n    };\n\n    const handleMouseDown = (e) => {\n      isDragging = true;\n      isHovering = true;\n      carouselTrack.classList.add('dragging');\n      startX = e.pageX;\n      startScrollLeft = carouselTrack.scrollLeft;\n    };\n\n    const handleMouseMove = (e) => {\n      if (!isDragging) return;\n      const x = e.pageX;\n      const walk = (x - startX) * 1.8;\n      carouselTrack.scrollLeft = startScrollLeft - walk;\n\n      const trackWidth = carouselTrack.scrollWidth / 2;\n      if (carouselTrack.scrollLeft >= trackWidth) {\n        carouselTrack.scrollLeft = 0;\n      } else if (carouselTrack.scrollLeft < 0) {\n        carouselTrack.scrollLeft = trackWidth - 1;\n      }\n    };\n\n    const handleMouseUp = () => {\n      if (isDragging) {\n        isDragging = false;\n        carouselTrack.classList.remove('dragging');\n        setTimeout(() => {\n          if (!isHovering) {\n            // Auto-scroll will resume\n          }\n        }, 100);\n      }\n    };\n\n    const handleWheel = (e) => {\n      e.preventDefault();\n      const wheelDelta = e.deltaY;\n      const scrollAmount = wheelDelta > 0 ? 50 : -50;\n      carouselTrack.scrollLeft += scrollAmount;\n\n      const trackWidth = carouselTrack.scrollWidth / 2;\n      if (carouselTrack.scrollLeft >= trackWidth) {\n        carouselTrack.scrollLeft = 0;\n      } else if (carouselTrack.scrollLeft < 0) {\n        carouselTrack.scrollLeft = trackWidth - 1;\n      }\n    };\n\n    // Add event listeners\n    carouselTrack.addEventListener('mouseenter', handleMouseEnter);\n    carouselTrack.addEventListener('mouseleave', handleMouseLeave);\n    carouselTrack.addEventListener('mousedown', handleMouseDown);\n    carouselTrack.addEventListener('mousemove', handleMouseMove);\n    document.addEventListener('mouseup', handleMouseUp);\n    carouselTrack.addEventListener('wheel', handleWheel, { passive: false });\n\n    // Start auto-scroll\n    startAutoScroll();\n\n    // Cleanup\n    return () => {\n      stopAutoScroll();\n      carouselTrack.removeEventListener('mouseenter', handleMouseEnter);\n      carouselTrack.removeEventListener('mouseleave', handleMouseLeave);\n      carouselTrack.removeEventListener('mousedown', handleMouseDown);\n      carouselTrack.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseup', handleMouseUp);\n      carouselTrack.removeEventListener('wheel', handleWheel);\n    };\n  }, []);\n\n  const portfolioItems = [\n    {\n      href: \"https://threed-e-commerce.onrender.com\",\n      image: \"/3D E-Comm.PNG\",\n      alt: \"3D Ecommerce\",\n      title: \"3D Ecommerce (Finish Soon)\"\n    },\n    {\n      href: \"#\",\n      image: \"/ex1.webp\",\n      alt: \"Yalla Go Posters\",\n      title: \"Will be deployed soon.\"\n    },\n    {\n      href: \"#\",\n      image: \"/ex2.png\",\n      alt: \"Nexit Brand Identity\",\n      title: \"Will be deployed soon.\"\n    },\n    {\n      href: \"#\",\n      image: \"/ex3.webp\",\n      alt: \"Yalla Go Posters\",\n      title: \"Will be deployed soon.\"\n    },\n    {\n      href: \"#\",\n      image: \"/ex4.1.png\",\n      alt: \"Yalla Go Posters\",\n      title: \"Will be deployed soon.\"\n    },\n    {\n      href: \"#\",\n      image: \"/ex5.png\",\n      alt: \"Yalla Go Posters\",\n      title: \"Will be deployed soon.\"\n    },\n    {\n      href: \"#\",\n      image: \"/bussniss web UI.PNG\",\n      alt: \"Business Web UI\",\n      title: \"Available in git Will be deployed soon.\"\n    }\n  ];\n\n  return (\n    <section className=\"portfolio\">\n      <h2>Top Projects<br /></h2>\n      <a href=\"#\" className=\"discover-button\">DISCOVER MORE</a>\n      <div className=\"portfolio-carousel\">\n        <div className=\"carousel-track\" ref={carouselTrackRef}>\n          {/* Render items twice for infinite scroll */}\n          {[...portfolioItems, ...portfolioItems].map((item, index) => (\n            <div key={index} className=\"portfolio-item\">\n              <a href={item.href} target=\"_blank\" rel=\"noopener noreferrer\">\n                <img src={item.image} alt={item.alt} />\n                <p>{item.title}</p>\n              </a>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Portfolio;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEjD,KAAM,CAAAC,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAAAC,gBAAgB,CAAGN,MAAM,CAAC,IAAI,CAAC,CAErCD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAQ,aAAa,CAAGD,gBAAgB,CAACE,OAAO,CAC9C,GAAI,CAACD,aAAa,CAAE,OAEpB,GAAI,CAAAE,UAAU,CAAG,KAAK,CACtB,GAAI,CAAAC,UAAU,CAAG,KAAK,CACtB,GAAI,CAAAC,MAAM,CACV,GAAI,CAAAC,eAAe,CACnB,GAAI,CAAAC,kBAAkB,CACtB,GAAI,CAAAC,WAAW,CAAG,CAAC,CAEnB;AACA,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAIF,kBAAkB,CAAEG,aAAa,CAACH,kBAAkB,CAAC,CACzDA,kBAAkB,CAAGI,WAAW,CAAC,IAAM,CACrC,GAAI,CAACR,UAAU,EAAI,CAACC,UAAU,CAAE,CAC9BH,aAAa,CAACW,UAAU,EAAIJ,WAAW,CACvC,KAAM,CAAAK,UAAU,CAAGZ,aAAa,CAACa,WAAW,CAAG,CAAC,CAChD,GAAIb,aAAa,CAACW,UAAU,EAAIC,UAAU,CAAE,CAC1CZ,aAAa,CAACW,UAAU,CAAG,CAAC,CAC9B,CACF,CACF,CAAC,CAAE,EAAE,CAAC,CACR,CAAC,CAED,KAAM,CAAAG,cAAc,CAAGA,CAAA,GAAM,CAC3B,GAAIR,kBAAkB,CAAE,CACtBG,aAAa,CAACH,kBAAkB,CAAC,CACjCA,kBAAkB,CAAG,IAAI,CAC3B,CACF,CAAC,CAED;AACA,KAAM,CAAAS,gBAAgB,CAAGA,CAAA,GAAM,CAC7BZ,UAAU,CAAG,IAAI,CACnB,CAAC,CAED,KAAM,CAAAa,gBAAgB,CAAGA,CAAA,GAAM,CAC7Bb,UAAU,CAAG,KAAK,CACpB,CAAC,CAED,KAAM,CAAAc,eAAe,CAAIC,CAAC,EAAK,CAC7BhB,UAAU,CAAG,IAAI,CACjBC,UAAU,CAAG,IAAI,CACjBH,aAAa,CAACmB,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC,CACvChB,MAAM,CAAGc,CAAC,CAACG,KAAK,CAChBhB,eAAe,CAAGL,aAAa,CAACW,UAAU,CAC5C,CAAC,CAED,KAAM,CAAAW,eAAe,CAAIJ,CAAC,EAAK,CAC7B,GAAI,CAAChB,UAAU,CAAE,OACjB,KAAM,CAAAqB,CAAC,CAAGL,CAAC,CAACG,KAAK,CACjB,KAAM,CAAAG,IAAI,CAAG,CAACD,CAAC,CAAGnB,MAAM,EAAI,GAAG,CAC/BJ,aAAa,CAACW,UAAU,CAAGN,eAAe,CAAGmB,IAAI,CAEjD,KAAM,CAAAZ,UAAU,CAAGZ,aAAa,CAACa,WAAW,CAAG,CAAC,CAChD,GAAIb,aAAa,CAACW,UAAU,EAAIC,UAAU,CAAE,CAC1CZ,aAAa,CAACW,UAAU,CAAG,CAAC,CAC9B,CAAC,IAAM,IAAIX,aAAa,CAACW,UAAU,CAAG,CAAC,CAAE,CACvCX,aAAa,CAACW,UAAU,CAAGC,UAAU,CAAG,CAAC,CAC3C,CACF,CAAC,CAED,KAAM,CAAAa,aAAa,CAAGA,CAAA,GAAM,CAC1B,GAAIvB,UAAU,CAAE,CACdA,UAAU,CAAG,KAAK,CAClBF,aAAa,CAACmB,SAAS,CAACO,MAAM,CAAC,UAAU,CAAC,CAC1CC,UAAU,CAAC,IAAM,CACf,GAAI,CAACxB,UAAU,CAAE,CACf;AAAA,CAEJ,CAAC,CAAE,GAAG,CAAC,CACT,CACF,CAAC,CAED,KAAM,CAAAyB,WAAW,CAAIV,CAAC,EAAK,CACzBA,CAAC,CAACW,cAAc,CAAC,CAAC,CAClB,KAAM,CAAAC,UAAU,CAAGZ,CAAC,CAACa,MAAM,CAC3B,KAAM,CAAAC,YAAY,CAAGF,UAAU,CAAG,CAAC,CAAG,EAAE,CAAG,CAAC,EAAE,CAC9C9B,aAAa,CAACW,UAAU,EAAIqB,YAAY,CAExC,KAAM,CAAApB,UAAU,CAAGZ,aAAa,CAACa,WAAW,CAAG,CAAC,CAChD,GAAIb,aAAa,CAACW,UAAU,EAAIC,UAAU,CAAE,CAC1CZ,aAAa,CAACW,UAAU,CAAG,CAAC,CAC9B,CAAC,IAAM,IAAIX,aAAa,CAACW,UAAU,CAAG,CAAC,CAAE,CACvCX,aAAa,CAACW,UAAU,CAAGC,UAAU,CAAG,CAAC,CAC3C,CACF,CAAC,CAED;AACAZ,aAAa,CAACiC,gBAAgB,CAAC,YAAY,CAAElB,gBAAgB,CAAC,CAC9Df,aAAa,CAACiC,gBAAgB,CAAC,YAAY,CAAEjB,gBAAgB,CAAC,CAC9DhB,aAAa,CAACiC,gBAAgB,CAAC,WAAW,CAAEhB,eAAe,CAAC,CAC5DjB,aAAa,CAACiC,gBAAgB,CAAC,WAAW,CAAEX,eAAe,CAAC,CAC5DY,QAAQ,CAACD,gBAAgB,CAAC,SAAS,CAAER,aAAa,CAAC,CACnDzB,aAAa,CAACiC,gBAAgB,CAAC,OAAO,CAAEL,WAAW,CAAE,CAAEO,OAAO,CAAE,KAAM,CAAC,CAAC,CAExE;AACA3B,eAAe,CAAC,CAAC,CAEjB;AACA,MAAO,IAAM,CACXM,cAAc,CAAC,CAAC,CAChBd,aAAa,CAACoC,mBAAmB,CAAC,YAAY,CAAErB,gBAAgB,CAAC,CACjEf,aAAa,CAACoC,mBAAmB,CAAC,YAAY,CAAEpB,gBAAgB,CAAC,CACjEhB,aAAa,CAACoC,mBAAmB,CAAC,WAAW,CAAEnB,eAAe,CAAC,CAC/DjB,aAAa,CAACoC,mBAAmB,CAAC,WAAW,CAAEd,eAAe,CAAC,CAC/DY,QAAQ,CAACE,mBAAmB,CAAC,SAAS,CAAEX,aAAa,CAAC,CACtDzB,aAAa,CAACoC,mBAAmB,CAAC,OAAO,CAAER,WAAW,CAAC,CACzD,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAS,cAAc,CAAG,CACrB,CACEC,IAAI,CAAE,wCAAwC,CAC9CC,KAAK,CAAE,gBAAgB,CACvBC,GAAG,CAAE,cAAc,CACnBC,KAAK,CAAE,4BACT,CAAC,CACD,CACEH,IAAI,CAAE,GAAG,CACTC,KAAK,CAAE,WAAW,CAClBC,GAAG,CAAE,kBAAkB,CACvBC,KAAK,CAAE,wBACT,CAAC,CACD,CACEH,IAAI,CAAE,GAAG,CACTC,KAAK,CAAE,UAAU,CACjBC,GAAG,CAAE,sBAAsB,CAC3BC,KAAK,CAAE,wBACT,CAAC,CACD,CACEH,IAAI,CAAE,GAAG,CACTC,KAAK,CAAE,WAAW,CAClBC,GAAG,CAAE,kBAAkB,CACvBC,KAAK,CAAE,wBACT,CAAC,CACD,CACEH,IAAI,CAAE,GAAG,CACTC,KAAK,CAAE,YAAY,CACnBC,GAAG,CAAE,kBAAkB,CACvBC,KAAK,CAAE,wBACT,CAAC,CACD,CACEH,IAAI,CAAE,GAAG,CACTC,KAAK,CAAE,UAAU,CACjBC,GAAG,CAAE,kBAAkB,CACvBC,KAAK,CAAE,wBACT,CAAC,CACD,CACEH,IAAI,CAAE,GAAG,CACTC,KAAK,CAAE,sBAAsB,CAC7BC,GAAG,CAAE,iBAAiB,CACtBC,KAAK,CAAE,yCACT,CAAC,CACF,CAED,mBACE5C,KAAA,YAAS6C,SAAS,CAAC,WAAW,CAAAC,QAAA,eAC5B9C,KAAA,OAAA8C,QAAA,EAAI,cAAY,cAAAhD,IAAA,QAAK,CAAC,EAAI,CAAC,cAC3BA,IAAA,MAAG2C,IAAI,CAAC,GAAG,CAACI,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,eAAa,CAAG,CAAC,cACzDhD,IAAA,QAAK+C,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjChD,IAAA,QAAK+C,SAAS,CAAC,gBAAgB,CAACE,GAAG,CAAE7C,gBAAiB,CAAA4C,QAAA,CAEnD,CAAC,GAAGN,cAAc,CAAE,GAAGA,cAAc,CAAC,CAACQ,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBACtDpD,IAAA,QAAiB+C,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cACzC9C,KAAA,MAAGyC,IAAI,CAAEQ,IAAI,CAACR,IAAK,CAACU,MAAM,CAAC,QAAQ,CAACC,GAAG,CAAC,qBAAqB,CAAAN,QAAA,eAC3DhD,IAAA,QAAKuD,GAAG,CAAEJ,IAAI,CAACP,KAAM,CAACC,GAAG,CAAEM,IAAI,CAACN,GAAI,CAAE,CAAC,cACvC7C,IAAA,MAAAgD,QAAA,CAAIG,IAAI,CAACL,KAAK,CAAI,CAAC,EAClB,CAAC,EAJIM,KAKL,CACN,CAAC,CACC,CAAC,CACH,CAAC,EACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAAjD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}