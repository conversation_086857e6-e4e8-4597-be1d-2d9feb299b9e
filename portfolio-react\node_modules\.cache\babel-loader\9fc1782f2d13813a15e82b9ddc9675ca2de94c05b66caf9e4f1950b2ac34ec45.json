{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfulio\\\\portfolio-react\\\\src\\\\components\\\\Footer.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"footer\", {\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"2025 REECRAFT. ALL RIGHTS RESERVED.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"back-to-top\",\n      onClick: () => window.scrollTo({\n        top: 0,\n        behavior: 'smooth'\n      }),\n      \"aria-label\": \"Back to top\",\n      children: \"\\u2191\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Footer", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "onClick", "window", "scrollTo", "top", "behavior", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/Footer.js"], "sourcesContent": ["import React from 'react';\n\nconst Footer = () => {\n  return (\n    <>\n      <footer>\n        <p>2025 REECRAFT. ALL RIGHTS RESERVED.</p>\n      </footer>\n      <button\n        className=\"back-to-top\"\n        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}\n        aria-label=\"Back to top\"\n      >\n        ↑\n      </button>\n    </>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,oBACEH,OAAA,CAAAE,SAAA;IAAAE,QAAA,gBACEJ,OAAA;MAAAI,QAAA,eACEJ,OAAA;QAAAI,QAAA,EAAG;MAAmC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC,eACTR,OAAA;MACES,SAAS,EAAC,aAAa;MACvBC,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAAC;QAAEC,GAAG,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAE;MAC/D,cAAW,aAAa;MAAAV,QAAA,EACzB;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA,eACT,CAAC;AAEP,CAAC;AAACO,EAAA,GAfIZ,MAAM;AAiBZ,eAAeA,MAAM;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}