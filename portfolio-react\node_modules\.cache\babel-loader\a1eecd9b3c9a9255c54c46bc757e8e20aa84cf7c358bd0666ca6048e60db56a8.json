{"ast": null, "code": "export const jobsData = [{\n  id: 1,\n  slug: \"frontend-receeto\",\n  title: \"Frontend Developer\",\n  company: \"Receeto\",\n  duration: \"3/2025 - 6/2025\",\n  logo: \"/Receeto_logo.jpg\",\n  logoAlt: \"Receeto Logo\",\n  summary: \"A smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights.\",\n  roleOverview: \"Note: Due to an active (NDA) contract, I'm unable to share detailed specifics about the project. However, I can briefly describe the technical scope and my personal contributions without disclosing confidential information.\\n\\nIt's a smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights.\\n\\nKey Highlights:\\nExpense Tracking & Budgeting: Tools for monitoring transactions and setting personalized financial goals.\\n\\nSpending Analytics: Interactive category-based analysis to help users make informed decisions.\\n\\nPerformance Optimization:\\n• Lazy loading for improved speed\\n• Critical CSS and production build optimization\\n\\nTech Stack & Architecture:\\n• Angular SPA with reactive state (signals)\\n• Fully responsive design for mobile and desktop\\n• Custom financial data visualizations using charts\\n\\nThis project showcases my expertise in Angular development, performance tuning, and crafting scalable, user-centric interfaces—while respecting the confidentiality of the client's program.\",\n  responsibilities: [\"Develop responsive web applications using Angular and modern frontend technologies\", \"Implement financial data visualizations and interactive charts\", \"Optimize application performance through lazy loading and build optimization\", \"Create expense tracking and budgeting tools with real-time data processing\", \"Build responsive interfaces for both mobile and desktop platforms\", \"Implement Angular reactive state management using signals\"],\n  skills: {\n    \"Frontend\": [\"Angular\", \"TypeScript\", \"RxJS\", \"Angular Signals\", \"Angular CLI\"],\n    \"Styling\": [\"CSS3\", \"SASS/SCSS\", \"Angular Material\", \"Responsive Design\", \"Bootstrap\"],\n    \"Tools & Testing\": [\"Git\", \"Angular CLI\", \"Webpack\", \"Lighthouse (for performance auditing)\", \"Figma\"]\n  },\n  accomplishments: [{\n    metric: \"40%\",\n    description: \"Improved application performance through lazy loading and build optimization\"\n  }, {\n    metric: \"100%\",\n    description: \"Responsive design compatibility across mobile and desktop platforms\"\n  }, {\n    metric: \"NDA\",\n    description: \"Confidential project delivered successfully while maintaining client privacy\"\n  }],\n  projects: [{\n    title: \"Project 1\",\n    description: \"NDA - details confidential\",\n    image: \"https://placehold.co/400x250/333333/ffffff?text=NDA+CONFIDENTIAL\",\n    technologies: [\"Angular\", \"TypeScript\", \"Charts.js\"]\n  }, {\n    title: \"Project 2\",\n    description: \"NDA - details confidential\",\n    image: \"https://placehold.co/400x250/333333/ffffff?text=NDA+CONFIDENTIAL\",\n    technologies: [\"Angular\", \"RxJS\", \"Angular Material\"]\n  }]\n}, {\n  id: 2,\n  slug: \"uiux-frontend-developer\",\n  title: \"UI/UX Designer & Frontend Developer\",\n  company: \"DigitalStudio Creative\",\n  duration: \"2022 - 2023\",\n  logo: \"https://via.placeholder.com/120x120/FF2D55/FFFFFF?text=DS\",\n  logoAlt: \"DigitalStudio Creative Logo\",\n  summary: \"Designed and developed responsive websites and mobile applications. Collaborated with clients to create compelling brand identities and user experiences that increased engagement by 40%.\",\n  roleOverview: \"At DigitalStudio Creative, I bridged the gap between design and development, creating seamless user experiences from concept to implementation. My dual role allowed me to ensure design integrity throughout the development process while maintaining optimal performance and accessibility standards.\",\n  responsibilities: [\"Design user interfaces and experiences for web and mobile applications\", \"Conduct user research and usability testing to inform design decisions\", \"Develop responsive frontend applications using modern frameworks\", \"Collaborate with clients to understand business requirements and user needs\", \"Create and maintain design systems and component libraries\", \"Optimize applications for performance and accessibility\"],\n  skills: {\n    \"Design Tools\": [\"Figma\", \"Adobe XD\", \"Sketch\", \"Photoshop\", \"Illustrator\"],\n    \"Frontend Development\": [\"React.js\", \"Vue.js\", \"JavaScript\", \"SASS/SCSS\", \"Bootstrap\"],\n    \"UX Research\": [\"User Testing\", \"Wireframing\", \"Prototyping\", \"Analytics\", \"A/B Testing\"]\n  },\n  accomplishments: [{\n    metric: \"40%\",\n    description: \"Increase in user engagement through improved UX design\"\n  }, {\n    metric: \"25+\",\n    description: \"Successful client projects delivered on time and budget\"\n  }, {\n    metric: \"95%\",\n    description: \"Client satisfaction rate based on project feedback\"\n  }, {\n    metric: \"3\",\n    description: \"Design awards received for outstanding user experience\"\n  }],\n  projects: [{\n    title: \"Mobile Banking Application\",\n    description: \"Designed and developed a secure, user-friendly mobile banking app with intuitive navigation\",\n    image: \"https://via.placeholder.com/400x250/FF2D55/FFFFFF?text=Mobile+Banking+App\",\n    technologies: [\"React Native\", \"Figma\", \"UX Research\"]\n  }, {\n    title: \"Interactive E-Learning Platform\",\n    description: \"Created engaging educational interface with gamification elements and progress tracking\",\n    image: \"https://via.placeholder.com/400x250/4B0082/FFFFFF?text=E-Learning+Platform\",\n    technologies: [\"Vue.js\", \"SCSS\", \"Adobe XD\"]\n  }, {\n    title: \"Complete Brand Identity System\",\n    description: \"Developed comprehensive brand guidelines and digital assets for startup company\",\n    image: \"https://via.placeholder.com/400x250/00CED1/FFFFFF?text=Brand+Identity+System\",\n    technologies: [\"Illustrator\", \"Photoshop\", \"Brand Strategy\"]\n  }]\n}, {\n  id: 3,\n  slug: \"junior-web-developer\",\n  title: \"Junior Web Developer\",\n  company: \"WebDev Agency\",\n  duration: \"2021 - 2022\",\n  logo: \"https://via.placeholder.com/120x120/00CED1/FFFFFF?text=WD\",\n  logoAlt: \"WebDev Agency Logo\",\n  summary: \"Developed custom WordPress themes and e-commerce solutions. Gained expertise in HTML, CSS, JavaScript, and PHP while working on diverse client projects ranging from small businesses to enterprise solutions.\",\n  roleOverview: \"As a Junior Web Developer at WebDev Agency, I focused on building custom websites and e-commerce solutions for a diverse range of clients. This role provided me with a solid foundation in web development fundamentals and client communication skills.\",\n  responsibilities: [\"Develop custom WordPress themes and plugins\", \"Build responsive websites using HTML, CSS, and JavaScript\", \"Create e-commerce solutions using WooCommerce and Shopify\", \"Collaborate with designers to implement pixel-perfect designs\", \"Optimize websites for performance and SEO\", \"Provide technical support and maintenance for client websites\"],\n  skills: {\n    \"Frontend\": [\"HTML5\", \"CSS3\", \"JavaScript\", \"jQuery\", \"Bootstrap\"],\n    \"Backend\": [\"PHP\", \"MySQL\", \"WordPress\", \"WooCommerce\"],\n    \"Tools\": [\"Git\", \"Photoshop\", \"Chrome DevTools\", \"FTP\", \"cPanel\"]\n  },\n  accomplishments: [{\n    metric: \"30+\",\n    description: \"Websites successfully developed and launched\"\n  }, {\n    metric: \"50%\",\n    description: \"Improvement in page load speeds through optimization\"\n  }, {\n    metric: \"100%\",\n    description: \"Client satisfaction rate for delivered projects\"\n  }],\n  projects: [{\n    title: \"Restaurant Chain Website\",\n    description: \"Built a multi-location restaurant website with online ordering system\",\n    image: \"https://via.placeholder.com/400x250/00CED1/FFFFFF?text=Restaurant+Website\",\n    technologies: [\"WordPress\", \"WooCommerce\", \"PHP\"]\n  }, {\n    title: \"Real Estate Portal\",\n    description: \"Developed property listing website with advanced search functionality\",\n    image: \"https://via.placeholder.com/400x250/32CD32/FFFFFF?text=Real+Estate+Portal\",\n    technologies: [\"HTML\", \"CSS\", \"JavaScript\", \"PHP\"]\n  }]\n}, {\n  id: 4,\n  slug: \"freelance-designer\",\n  title: \"Freelance Designer\",\n  company: \"Self-Employed\",\n  duration: \"2020 - 2021\",\n  logo: \"https://via.placeholder.com/120x120/32CD32/FFFFFF?text=FL\",\n  logoAlt: \"Freelance Logo\",\n  summary: \"Started my journey as a freelance graphic designer, creating logos, branding materials, and marketing collateral for local businesses. Built a strong foundation in design principles and client communication.\",\n  roleOverview: \"Beginning my career as a freelance designer, I worked with local businesses to create compelling visual identities and marketing materials. This experience taught me the importance of understanding client needs and translating business objectives into effective design solutions.\",\n  responsibilities: [\"Design logos and brand identities for small businesses\", \"Create marketing materials including flyers, brochures, and business cards\", \"Develop social media graphics and digital marketing assets\", \"Collaborate directly with business owners to understand their vision\", \"Manage multiple projects simultaneously while meeting deadlines\", \"Handle client communications and project billing\"],\n  skills: {\n    \"Design Software\": [\"Adobe Illustrator\", \"Adobe Photoshop\", \"Adobe InDesign\", \"Canva\"],\n    \"Design Skills\": [\"Logo Design\", \"Brand Identity\", \"Print Design\", \"Digital Graphics\"],\n    \"Business Skills\": [\"Client Communication\", \"Project Management\", \"Time Management\", \"Pricing\"]\n  },\n  accomplishments: [{\n    metric: \"20+\",\n    description: \"Local businesses served with design solutions\"\n  }, {\n    metric: \"4.9/5\",\n    description: \"Average client rating on freelance platforms\"\n  }, {\n    metric: \"90%\",\n    description: \"Client retention rate for ongoing projects\"\n  }],\n  projects: [{\n    title: \"Local Coffee Shop Branding\",\n    description: \"Complete brand identity including logo, menu design, and signage\",\n    image: \"https://via.placeholder.com/400x250/32CD32/FFFFFF?text=Coffee+Shop+Brand\",\n    technologies: [\"Illustrator\", \"Photoshop\", \"InDesign\"]\n  }, {\n    title: \"Fitness Studio Marketing Kit\",\n    description: \"Comprehensive marketing materials for new fitness studio launch\",\n    image: \"https://via.placeholder.com/400x250/FF6347/FFFFFF?text=Fitness+Marketing\",\n    technologies: [\"Photoshop\", \"Illustrator\", \"Print Design\"]\n  }]\n}];", "map": {"version": 3, "names": ["jobsData", "id", "slug", "title", "company", "duration", "logo", "logoAlt", "summary", "roleOverview", "responsibilities", "skills", "accomplishments", "metric", "description", "projects", "image", "technologies"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/data/jobsData.js"], "sourcesContent": ["export const jobsData = [\n  {\n    id: 1,\n    slug: \"frontend-receeto\",\n    title: \"Frontend Developer\",\n    company: \"Receeto\",\n    duration: \"3/2025 - 6/2025\",\n    logo: \"/Receeto_logo.jpg\",\n    logoAlt: \"Receeto Logo\",\n    summary: \"A smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights.\",\n    roleOverview: \"Note: Due to an active (NDA) contract, I'm unable to share detailed specifics about the project. However, I can briefly describe the technical scope and my personal contributions without disclosing confidential information.\\n\\nIt's a smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights.\\n\\nKey Highlights:\\nExpense Tracking & Budgeting: Tools for monitoring transactions and setting personalized financial goals.\\n\\nSpending Analytics: Interactive category-based analysis to help users make informed decisions.\\n\\nPerformance Optimization:\\n• Lazy loading for improved speed\\n• Critical CSS and production build optimization\\n\\nTech Stack & Architecture:\\n• Angular SPA with reactive state (signals)\\n• Fully responsive design for mobile and desktop\\n• Custom financial data visualizations using charts\\n\\nThis project showcases my expertise in Angular development, performance tuning, and crafting scalable, user-centric interfaces—while respecting the confidentiality of the client's program.\",\n    responsibilities: [\n      \"Develop responsive web applications using Angular and modern frontend technologies\",\n      \"Implement financial data visualizations and interactive charts\",\n      \"Optimize application performance through lazy loading and build optimization\",\n      \"Create expense tracking and budgeting tools with real-time data processing\",\n      \"Build responsive interfaces for both mobile and desktop platforms\",\n      \"Implement Angular reactive state management using signals\"\n    ],\n    skills: {\n      \"Frontend\": [\"Angular\", \"TypeScript\", \"RxJS\", \"Angular Signals\", \"Angular CLI\"],\n      \"Styling\": [\"CSS3\", \"SASS/SCSS\", \"Angular Material\", \"Responsive Design\", \"Bootstrap\"],\n      \"Tools & Testing\": [\"Git\", \"Angular CLI\", \"Webpack\", \"Lighthouse (for performance auditing)\", \"Figma\"]\n    },\n    accomplishments: [\n      {\n        metric: \"40%\",\n        description: \"Improved application performance through lazy loading and build optimization\"\n      },\n      {\n        metric: \"100%\",\n        description: \"Responsive design compatibility across mobile and desktop platforms\"\n      },\n      {\n        metric: \"NDA\",\n        description: \"Confidential project delivered successfully while maintaining client privacy\"\n      }\n    ],\n    projects: [\n      {\n        title: \"Project 1\",\n        description: \"NDA - details confidential\",\n        image: \"https://placehold.co/400x250/333333/ffffff?text=NDA+CONFIDENTIAL\",\n        technologies: [\"Angular\", \"TypeScript\", \"Charts.js\"]\n      },\n      {\n        title: \"Project 2\",\n        description: \"NDA - details confidential\",\n        image: \"https://placehold.co/400x250/333333/ffffff?text=NDA+CONFIDENTIAL\",\n        technologies: [\"Angular\", \"RxJS\", \"Angular Material\"]\n      }\n    ]\n  },\n  {\n    id: 2,\n    slug: \"uiux-frontend-developer\",\n    title: \"UI/UX Designer & Frontend Developer\",\n    company: \"DigitalStudio Creative\",\n    duration: \"2022 - 2023\",\n    logo: \"https://via.placeholder.com/120x120/FF2D55/FFFFFF?text=DS\",\n    logoAlt: \"DigitalStudio Creative Logo\",\n    summary: \"Designed and developed responsive websites and mobile applications. Collaborated with clients to create compelling brand identities and user experiences that increased engagement by 40%.\",\n    roleOverview: \"At DigitalStudio Creative, I bridged the gap between design and development, creating seamless user experiences from concept to implementation. My dual role allowed me to ensure design integrity throughout the development process while maintaining optimal performance and accessibility standards.\",\n    responsibilities: [\n      \"Design user interfaces and experiences for web and mobile applications\",\n      \"Conduct user research and usability testing to inform design decisions\",\n      \"Develop responsive frontend applications using modern frameworks\",\n      \"Collaborate with clients to understand business requirements and user needs\",\n      \"Create and maintain design systems and component libraries\",\n      \"Optimize applications for performance and accessibility\"\n    ],\n    skills: {\n      \"Design Tools\": [\"Figma\", \"Adobe XD\", \"Sketch\", \"Photoshop\", \"Illustrator\"],\n      \"Frontend Development\": [\"React.js\", \"Vue.js\", \"JavaScript\", \"SASS/SCSS\", \"Bootstrap\"],\n      \"UX Research\": [\"User Testing\", \"Wireframing\", \"Prototyping\", \"Analytics\", \"A/B Testing\"]\n    },\n    accomplishments: [\n      {\n        metric: \"40%\",\n        description: \"Increase in user engagement through improved UX design\"\n      },\n      {\n        metric: \"25+\",\n        description: \"Successful client projects delivered on time and budget\"\n      },\n      {\n        metric: \"95%\",\n        description: \"Client satisfaction rate based on project feedback\"\n      },\n      {\n        metric: \"3\",\n        description: \"Design awards received for outstanding user experience\"\n      }\n    ],\n    projects: [\n      {\n        title: \"Mobile Banking Application\",\n        description: \"Designed and developed a secure, user-friendly mobile banking app with intuitive navigation\",\n        image: \"https://via.placeholder.com/400x250/FF2D55/FFFFFF?text=Mobile+Banking+App\",\n        technologies: [\"React Native\", \"Figma\", \"UX Research\"]\n      },\n      {\n        title: \"Interactive E-Learning Platform\",\n        description: \"Created engaging educational interface with gamification elements and progress tracking\",\n        image: \"https://via.placeholder.com/400x250/4B0082/FFFFFF?text=E-Learning+Platform\",\n        technologies: [\"Vue.js\", \"SCSS\", \"Adobe XD\"]\n      },\n      {\n        title: \"Complete Brand Identity System\",\n        description: \"Developed comprehensive brand guidelines and digital assets for startup company\",\n        image: \"https://via.placeholder.com/400x250/00CED1/FFFFFF?text=Brand+Identity+System\",\n        technologies: [\"Illustrator\", \"Photoshop\", \"Brand Strategy\"]\n      }\n    ]\n  },\n  {\n    id: 3,\n    slug: \"junior-web-developer\",\n    title: \"Junior Web Developer\",\n    company: \"WebDev Agency\",\n    duration: \"2021 - 2022\",\n    logo: \"https://via.placeholder.com/120x120/00CED1/FFFFFF?text=WD\",\n    logoAlt: \"WebDev Agency Logo\",\n    summary: \"Developed custom WordPress themes and e-commerce solutions. Gained expertise in HTML, CSS, JavaScript, and PHP while working on diverse client projects ranging from small businesses to enterprise solutions.\",\n    roleOverview: \"As a Junior Web Developer at WebDev Agency, I focused on building custom websites and e-commerce solutions for a diverse range of clients. This role provided me with a solid foundation in web development fundamentals and client communication skills.\",\n    responsibilities: [\n      \"Develop custom WordPress themes and plugins\",\n      \"Build responsive websites using HTML, CSS, and JavaScript\",\n      \"Create e-commerce solutions using WooCommerce and Shopify\",\n      \"Collaborate with designers to implement pixel-perfect designs\",\n      \"Optimize websites for performance and SEO\",\n      \"Provide technical support and maintenance for client websites\"\n    ],\n    skills: {\n      \"Frontend\": [\"HTML5\", \"CSS3\", \"JavaScript\", \"jQuery\", \"Bootstrap\"],\n      \"Backend\": [\"PHP\", \"MySQL\", \"WordPress\", \"WooCommerce\"],\n      \"Tools\": [\"Git\", \"Photoshop\", \"Chrome DevTools\", \"FTP\", \"cPanel\"]\n    },\n    accomplishments: [\n      {\n        metric: \"30+\",\n        description: \"Websites successfully developed and launched\"\n      },\n      {\n        metric: \"50%\",\n        description: \"Improvement in page load speeds through optimization\"\n      },\n      {\n        metric: \"100%\",\n        description: \"Client satisfaction rate for delivered projects\"\n      }\n    ],\n    projects: [\n      {\n        title: \"Restaurant Chain Website\",\n        description: \"Built a multi-location restaurant website with online ordering system\",\n        image: \"https://via.placeholder.com/400x250/00CED1/FFFFFF?text=Restaurant+Website\",\n        technologies: [\"WordPress\", \"WooCommerce\", \"PHP\"]\n      },\n      {\n        title: \"Real Estate Portal\",\n        description: \"Developed property listing website with advanced search functionality\",\n        image: \"https://via.placeholder.com/400x250/32CD32/FFFFFF?text=Real+Estate+Portal\",\n        technologies: [\"HTML\", \"CSS\", \"JavaScript\", \"PHP\"]\n      }\n    ]\n  },\n  {\n    id: 4,\n    slug: \"freelance-designer\",\n    title: \"Freelance Designer\",\n    company: \"Self-Employed\",\n    duration: \"2020 - 2021\",\n    logo: \"https://via.placeholder.com/120x120/32CD32/FFFFFF?text=FL\",\n    logoAlt: \"Freelance Logo\",\n    summary: \"Started my journey as a freelance graphic designer, creating logos, branding materials, and marketing collateral for local businesses. Built a strong foundation in design principles and client communication.\",\n    roleOverview: \"Beginning my career as a freelance designer, I worked with local businesses to create compelling visual identities and marketing materials. This experience taught me the importance of understanding client needs and translating business objectives into effective design solutions.\",\n    responsibilities: [\n      \"Design logos and brand identities for small businesses\",\n      \"Create marketing materials including flyers, brochures, and business cards\",\n      \"Develop social media graphics and digital marketing assets\",\n      \"Collaborate directly with business owners to understand their vision\",\n      \"Manage multiple projects simultaneously while meeting deadlines\",\n      \"Handle client communications and project billing\"\n    ],\n    skills: {\n      \"Design Software\": [\"Adobe Illustrator\", \"Adobe Photoshop\", \"Adobe InDesign\", \"Canva\"],\n      \"Design Skills\": [\"Logo Design\", \"Brand Identity\", \"Print Design\", \"Digital Graphics\"],\n      \"Business Skills\": [\"Client Communication\", \"Project Management\", \"Time Management\", \"Pricing\"]\n    },\n    accomplishments: [\n      {\n        metric: \"20+\",\n        description: \"Local businesses served with design solutions\"\n      },\n      {\n        metric: \"4.9/5\",\n        description: \"Average client rating on freelance platforms\"\n      },\n      {\n        metric: \"90%\",\n        description: \"Client retention rate for ongoing projects\"\n      }\n    ],\n    projects: [\n      {\n        title: \"Local Coffee Shop Branding\",\n        description: \"Complete brand identity including logo, menu design, and signage\",\n        image: \"https://via.placeholder.com/400x250/32CD32/FFFFFF?text=Coffee+Shop+Brand\",\n        technologies: [\"Illustrator\", \"Photoshop\", \"InDesign\"]\n      },\n      {\n        title: \"Fitness Studio Marketing Kit\",\n        description: \"Comprehensive marketing materials for new fitness studio launch\",\n        image: \"https://via.placeholder.com/400x250/FF6347/FFFFFF?text=Fitness+Marketing\",\n        technologies: [\"Photoshop\", \"Illustrator\", \"Print Design\"]\n      }\n    ]\n  }\n];\n"], "mappings": "AAAA,OAAO,MAAMA,QAAQ,GAAG,CACtB;EACEC,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,kBAAkB;EACxBC,KAAK,EAAE,oBAAoB;EAC3BC,OAAO,EAAE,SAAS;EAClBC,QAAQ,EAAE,iBAAiB;EAC3BC,IAAI,EAAE,mBAAmB;EACzBC,OAAO,EAAE,cAAc;EACvBC,OAAO,EAAE,gJAAgJ;EACzJC,YAAY,EAAE,4jCAA4jC;EAC1kCC,gBAAgB,EAAE,CAChB,oFAAoF,EACpF,gEAAgE,EAChE,8EAA8E,EAC9E,4EAA4E,EAC5E,mEAAmE,EACnE,2DAA2D,CAC5D;EACDC,MAAM,EAAE;IACN,UAAU,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,MAAM,EAAE,iBAAiB,EAAE,aAAa,CAAC;IAC/E,SAAS,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,WAAW,CAAC;IACtF,iBAAiB,EAAE,CAAC,KAAK,EAAE,aAAa,EAAE,SAAS,EAAE,uCAAuC,EAAE,OAAO;EACvG,CAAC;EACDC,eAAe,EAAE,CACf;IACEC,MAAM,EAAE,KAAK;IACbC,WAAW,EAAE;EACf,CAAC,EACD;IACED,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE;EACf,CAAC,EACD;IACED,MAAM,EAAE,KAAK;IACbC,WAAW,EAAE;EACf,CAAC,CACF;EACDC,QAAQ,EAAE,CACR;IACEZ,KAAK,EAAE,WAAW;IAClBW,WAAW,EAAE,4BAA4B;IACzCE,KAAK,EAAE,kEAAkE;IACzEC,YAAY,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,WAAW;EACrD,CAAC,EACD;IACEd,KAAK,EAAE,WAAW;IAClBW,WAAW,EAAE,4BAA4B;IACzCE,KAAK,EAAE,kEAAkE;IACzEC,YAAY,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,kBAAkB;EACtD,CAAC;AAEL,CAAC,EACD;EACEhB,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,yBAAyB;EAC/BC,KAAK,EAAE,qCAAqC;EAC5CC,OAAO,EAAE,wBAAwB;EACjCC,QAAQ,EAAE,aAAa;EACvBC,IAAI,EAAE,2DAA2D;EACjEC,OAAO,EAAE,6BAA6B;EACtCC,OAAO,EAAE,4LAA4L;EACrMC,YAAY,EAAE,0SAA0S;EACxTC,gBAAgB,EAAE,CAChB,wEAAwE,EACxE,wEAAwE,EACxE,kEAAkE,EAClE,6EAA6E,EAC7E,4DAA4D,EAC5D,yDAAyD,CAC1D;EACDC,MAAM,EAAE;IACN,cAAc,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,CAAC;IAC3E,sBAAsB,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,CAAC;IACtF,aAAa,EAAE,CAAC,cAAc,EAAE,aAAa,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa;EAC1F,CAAC;EACDC,eAAe,EAAE,CACf;IACEC,MAAM,EAAE,KAAK;IACbC,WAAW,EAAE;EACf,CAAC,EACD;IACED,MAAM,EAAE,KAAK;IACbC,WAAW,EAAE;EACf,CAAC,EACD;IACED,MAAM,EAAE,KAAK;IACbC,WAAW,EAAE;EACf,CAAC,EACD;IACED,MAAM,EAAE,GAAG;IACXC,WAAW,EAAE;EACf,CAAC,CACF;EACDC,QAAQ,EAAE,CACR;IACEZ,KAAK,EAAE,4BAA4B;IACnCW,WAAW,EAAE,6FAA6F;IAC1GE,KAAK,EAAE,2EAA2E;IAClFC,YAAY,EAAE,CAAC,cAAc,EAAE,OAAO,EAAE,aAAa;EACvD,CAAC,EACD;IACEd,KAAK,EAAE,iCAAiC;IACxCW,WAAW,EAAE,yFAAyF;IACtGE,KAAK,EAAE,4EAA4E;IACnFC,YAAY,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU;EAC7C,CAAC,EACD;IACEd,KAAK,EAAE,gCAAgC;IACvCW,WAAW,EAAE,iFAAiF;IAC9FE,KAAK,EAAE,8EAA8E;IACrFC,YAAY,EAAE,CAAC,aAAa,EAAE,WAAW,EAAE,gBAAgB;EAC7D,CAAC;AAEL,CAAC,EACD;EACEhB,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,sBAAsB;EAC5BC,KAAK,EAAE,sBAAsB;EAC7BC,OAAO,EAAE,eAAe;EACxBC,QAAQ,EAAE,aAAa;EACvBC,IAAI,EAAE,2DAA2D;EACjEC,OAAO,EAAE,oBAAoB;EAC7BC,OAAO,EAAE,gNAAgN;EACzNC,YAAY,EAAE,2PAA2P;EACzQC,gBAAgB,EAAE,CAChB,6CAA6C,EAC7C,2DAA2D,EAC3D,2DAA2D,EAC3D,+DAA+D,EAC/D,2CAA2C,EAC3C,+DAA+D,CAChE;EACDC,MAAM,EAAE;IACN,UAAU,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,WAAW,CAAC;IAClE,SAAS,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,CAAC;IACvD,OAAO,EAAE,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,EAAE,KAAK,EAAE,QAAQ;EAClE,CAAC;EACDC,eAAe,EAAE,CACf;IACEC,MAAM,EAAE,KAAK;IACbC,WAAW,EAAE;EACf,CAAC,EACD;IACED,MAAM,EAAE,KAAK;IACbC,WAAW,EAAE;EACf,CAAC,EACD;IACED,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE;EACf,CAAC,CACF;EACDC,QAAQ,EAAE,CACR;IACEZ,KAAK,EAAE,0BAA0B;IACjCW,WAAW,EAAE,uEAAuE;IACpFE,KAAK,EAAE,2EAA2E;IAClFC,YAAY,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,KAAK;EAClD,CAAC,EACD;IACEd,KAAK,EAAE,oBAAoB;IAC3BW,WAAW,EAAE,uEAAuE;IACpFE,KAAK,EAAE,2EAA2E;IAClFC,YAAY,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK;EACnD,CAAC;AAEL,CAAC,EACD;EACEhB,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,oBAAoB;EAC1BC,KAAK,EAAE,oBAAoB;EAC3BC,OAAO,EAAE,eAAe;EACxBC,QAAQ,EAAE,aAAa;EACvBC,IAAI,EAAE,2DAA2D;EACjEC,OAAO,EAAE,gBAAgB;EACzBC,OAAO,EAAE,iNAAiN;EAC1NC,YAAY,EAAE,yRAAyR;EACvSC,gBAAgB,EAAE,CAChB,wDAAwD,EACxD,4EAA4E,EAC5E,4DAA4D,EAC5D,sEAAsE,EACtE,iEAAiE,EACjE,kDAAkD,CACnD;EACDC,MAAM,EAAE;IACN,iBAAiB,EAAE,CAAC,mBAAmB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,OAAO,CAAC;IACtF,eAAe,EAAE,CAAC,aAAa,EAAE,gBAAgB,EAAE,cAAc,EAAE,kBAAkB,CAAC;IACtF,iBAAiB,EAAE,CAAC,sBAAsB,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,SAAS;EAChG,CAAC;EACDC,eAAe,EAAE,CACf;IACEC,MAAM,EAAE,KAAK;IACbC,WAAW,EAAE;EACf,CAAC,EACD;IACED,MAAM,EAAE,OAAO;IACfC,WAAW,EAAE;EACf,CAAC,EACD;IACED,MAAM,EAAE,KAAK;IACbC,WAAW,EAAE;EACf,CAAC,CACF;EACDC,QAAQ,EAAE,CACR;IACEZ,KAAK,EAAE,4BAA4B;IACnCW,WAAW,EAAE,kEAAkE;IAC/EE,KAAK,EAAE,0EAA0E;IACjFC,YAAY,EAAE,CAAC,aAAa,EAAE,WAAW,EAAE,UAAU;EACvD,CAAC,EACD;IACEd,KAAK,EAAE,8BAA8B;IACrCW,WAAW,EAAE,iEAAiE;IAC9EE,KAAK,EAAE,0EAA0E;IACjFC,YAAY,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,cAAc;EAC3D,CAAC;AAEL,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}