{"ast": null, "code": "'use strict';\n\nvar classofRaw = require('../internals/classof-raw');\nvar uncurryThis = require('../internals/function-uncurry-this');\nmodule.exports = function (fn) {\n  // Nashorn bug:\n  //   https://github.com/zloirock/core-js/issues/1128\n  //   https://github.com/zloirock/core-js/issues/1130\n  if (classofRaw(fn) === 'Function') return uncurryThis(fn);\n};", "map": {"version": 3, "names": ["classofRaw", "require", "uncurryThis", "module", "exports", "fn"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/node_modules/core-js-pure/internals/function-uncurry-this-clause.js"], "sourcesContent": ["'use strict';\nvar classofRaw = require('../internals/classof-raw');\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nmodule.exports = function (fn) {\n  // Nashorn bug:\n  //   https://github.com/zloirock/core-js/issues/1128\n  //   https://github.com/zloirock/core-js/issues/1130\n  if (classofRaw(fn) === 'Function') return uncurryThis(fn);\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIC,WAAW,GAAGD,OAAO,CAAC,oCAAoC,CAAC;AAE/DE,MAAM,CAACC,OAAO,GAAG,UAAUC,EAAE,EAAE;EAC7B;EACA;EACA;EACA,IAAIL,UAAU,CAACK,EAAE,CAAC,KAAK,UAAU,EAAE,OAAOH,WAAW,CAACG,EAAE,CAAC;AAC3D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}