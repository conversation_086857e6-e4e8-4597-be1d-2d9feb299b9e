{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Intro=()=>{return/*#__PURE__*/_jsxs(\"section\",{className:\"intro\",children:[/*#__PURE__*/_jsxs(\"h1\",{children:[\"Welcome, I'M\",/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(\"span\",{className:\"highlight\",children:\"Chouchane Med Amine\"})]}),/*#__PURE__*/_jsx(\"p\",{children:\"Software Developer\"})]});};export default Intro;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "Intro", "className", "children"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/Intro.js"], "sourcesContent": ["import React from 'react';\n\nconst Intro = () => {\n  return (\n    <section className=\"intro\">\n      <h1>Welcome, I'M<br /><span className=\"highlight\">Chouchane Med Amine</span></h1>\n      <p>Software Developer</p>\n    </section>\n  );\n};\n\nexport default Intro;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,KAAK,CAAGA,CAAA,GAAM,CAClB,mBACED,KAAA,YAASE,SAAS,CAAC,OAAO,CAAAC,QAAA,eACxBH,KAAA,OAAAG,QAAA,EAAI,cAAY,cAAAL,IAAA,QAAK,CAAC,cAAAA,IAAA,SAAMI,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,qBAAmB,CAAM,CAAC,EAAI,CAAC,cACjFL,IAAA,MAAAK,QAAA,CAAG,oBAAkB,CAAG,CAAC,EAClB,CAAC,CAEd,CAAC,CAED,cAAe,CAAAF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}