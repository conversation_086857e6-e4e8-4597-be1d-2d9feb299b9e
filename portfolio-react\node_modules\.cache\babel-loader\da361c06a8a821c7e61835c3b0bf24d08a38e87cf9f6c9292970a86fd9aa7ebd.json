{"ast": null, "code": "import React from'react';import ReactDOM from'react-dom/client';import'./index.css';import App from'./App';import reportWebVitals from'./reportWebVitals';import{jsx as _jsx}from\"react/jsx-runtime\";const root=ReactDOM.createRoot(document.getElementById('root'));root.render(/*#__PURE__*/_jsx(React.StrictMode,{children:/*#__PURE__*/_jsx(App,{})}));// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "reportWebVitals", "jsx", "_jsx", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/index.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\nimport reportWebVitals from './reportWebVitals';\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>\n);\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,aAAa,CACpB,MAAO,CAAAC,GAAG,KAAM,OAAO,CACvB,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEhD,KAAM,CAAAC,IAAI,CAAGL,QAAQ,CAACM,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC,CACjEH,IAAI,CAACI,MAAM,cACTL,IAAA,CAACL,KAAK,CAACW,UAAU,EAAAC,QAAA,cACfP,IAAA,CAACH,GAAG,GAAE,CAAC,CACS,CACpB,CAAC,CAED;AACA;AACA;AACAC,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}