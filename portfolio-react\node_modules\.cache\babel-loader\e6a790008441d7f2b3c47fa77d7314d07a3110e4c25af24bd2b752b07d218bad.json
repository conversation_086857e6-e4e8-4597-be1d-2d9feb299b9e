{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ClientThoughts=()=>{return/*#__PURE__*/_jsxs(\"section\",{className:\"client-thoughts\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"quote-icon\"}),/*#__PURE__*/_jsx(\"h2\",{children:\"CLIENT THOUGHTS\"}),/*#__PURE__*/_jsx(\"h3\",{children:\"\\\"E-COMMERCE EXPERTISE\\\"\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Will be Available Soon . . . \"}),/*#__PURE__*/_jsx(\"div\",{className:\"thoughts-image\",children:/*#__PURE__*/_jsx(\"img\",{src:\"/client touch.jpg\",alt:\"E-commerce Expertise Visual\",style:{width:'100%',maxWidth:'600px',height:'auto',margin:'20px 0',border:'2px solid #FF2D55',borderRadius:'10px'}})})]});};export default ClientThoughts;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "ClientThoughts", "className", "children", "src", "alt", "style", "width", "max<PERSON><PERSON><PERSON>", "height", "margin", "border", "borderRadius"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/ClientThoughts.js"], "sourcesContent": ["import React from 'react';\n\nconst ClientThoughts = () => {\n  return (\n    <section className=\"client-thoughts\">\n      <div className=\"quote-icon\">\n        {/* Quote icon placeholder */}\n      </div>\n      <h2>CLIENT THOUGHTS</h2>\n      <h3>\"E-COMMERCE EXPERTISE\"</h3>\n      <p>Will be Available Soon . . . </p>\n      <div className=\"thoughts-image\">\n        <img \n          src=\"/client touch.jpg\" \n          alt=\"E-commerce Expertise Visual\" \n          style={{\n            width: '100%', \n            maxWidth: '600px', \n            height: 'auto', \n            margin: '20px 0', \n            border: '2px solid #FF2D55', \n            borderRadius: '10px'\n          }}\n        />\n      </div>\n    </section>\n  );\n};\n\nexport default ClientThoughts;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,mBACED,KAAA,YAASE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAClCL,IAAA,QAAKI,SAAS,CAAC,YAAY,CAEtB,CAAC,cACNJ,IAAA,OAAAK,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxBL,IAAA,OAAAK,QAAA,CAAI,0BAAsB,CAAI,CAAC,cAC/BL,IAAA,MAAAK,QAAA,CAAG,+BAA6B,CAAG,CAAC,cACpCL,IAAA,QAAKI,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BL,IAAA,QACEM,GAAG,CAAC,mBAAmB,CACvBC,GAAG,CAAC,6BAA6B,CACjCC,KAAK,CAAE,CACLC,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,OAAO,CACjBC,MAAM,CAAE,MAAM,CACdC,MAAM,CAAE,QAAQ,CAChBC,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,MAChB,CAAE,CACH,CAAC,CACC,CAAC,EACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAAX,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}