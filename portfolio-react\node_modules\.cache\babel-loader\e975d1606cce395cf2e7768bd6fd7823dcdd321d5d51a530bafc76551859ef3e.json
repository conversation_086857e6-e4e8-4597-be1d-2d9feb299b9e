{"ast": null, "code": "import React from'react';import{Link}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Header=()=>{return/*#__PURE__*/_jsxs(\"header\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"logo\",children:/*#__PURE__*/_jsx(Link,{to:\"/\",children:/*#__PURE__*/_jsx(\"img\",{src:\"/logo.PNG\",alt:\"Logo\",className:\"logo-img\"})})}),/*#__PURE__*/_jsx(\"a\",{href:\"https://www.canva.com/design/DAGNjKc0Cr0/nJ-kiMZTpFhVUD6B6nyPYg/view?utm_content=DAGNjKc0Cr0&utm_campaign=designshare&utm_medium=link2&utm_source=uniquelinks&utlId=h6802188a93\",className:\"cv-button\",target:\"_blank\",rel:\"noopener noreferrer\",children:\"Get CV\"})]});};export default Header;", "map": {"version": 3, "names": ["React", "Link", "jsx", "_jsx", "jsxs", "_jsxs", "Header", "children", "className", "to", "src", "alt", "href", "target", "rel"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/Header.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Header = () => {\n  return (\n    <header>\n      <div className=\"logo\">\n        <Link to=\"/\">\n          <img src=\"/logo.PNG\" alt=\"Logo\" className=\"logo-img\" />\n        </Link>\n      </div>\n      <a \n        href=\"https://www.canva.com/design/DAGNjKc0Cr0/nJ-kiMZTpFhVUD6B6nyPYg/view?utm_content=DAGNjKc0Cr0&utm_campaign=designshare&utm_medium=link2&utm_source=uniquelinks&utlId=h6802188a93\" \n        className=\"cv-button\"\n        target=\"_blank\"\n        rel=\"noopener noreferrer\"\n      >\n        Get CV\n      </a>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,KAAM,CAAAC,MAAM,CAAGA,CAAA,GAAM,CACnB,mBACED,KAAA,WAAAE,QAAA,eACEJ,IAAA,QAAKK,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBJ,IAAA,CAACF,IAAI,EAACQ,EAAE,CAAC,GAAG,CAAAF,QAAA,cACVJ,IAAA,QAAKO,GAAG,CAAC,WAAW,CAACC,GAAG,CAAC,MAAM,CAACH,SAAS,CAAC,UAAU,CAAE,CAAC,CACnD,CAAC,CACJ,CAAC,cACNL,IAAA,MACES,IAAI,CAAC,iLAAiL,CACtLJ,SAAS,CAAC,WAAW,CACrBK,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CAAAP,QAAA,CAC1B,QAED,CAAG,CAAC,EACE,CAAC,CAEb,CAAC,CAED,cAAe,CAAAD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}