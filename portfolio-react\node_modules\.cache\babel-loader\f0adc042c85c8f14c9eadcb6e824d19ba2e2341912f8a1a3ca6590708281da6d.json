{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Contact=()=>{return/*#__PURE__*/_jsx(\"section\",{className:\"contact\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"contact-overlay\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"LET'S CREATE TOGETHER\"}),/*#__PURE__*/_jsxs(\"form\",{action:\"https://formspree.io/f/mblgroaz\",method:\"POST\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"name\",children:\"YOUR NAME\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"name\",name:\"name\",placeholder:\"NAME\",required:true}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"email\",children:\"YOUR EMAIL\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",id:\"email\",name:\"email\",placeholder:\"EMAIL\",required:true}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"services\",children:\"WHAT SERVICES ARE YOU LOOKING FOR?\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"services\",name:\"services\",placeholder:\"Web Design, Graphic design...\",required:true}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"message\",children:\"YOUR MESSAGE\"}),/*#__PURE__*/_jsx(\"textarea\",{id:\"message\",name:\"message\",placeholder:\"YOUR MESSAGE\",required:true}),/*#__PURE__*/_jsxs(\"label\",{htmlFor:\"linkedin\",children:[\"Your LinkedIn \",/*#__PURE__*/_jsx(\"span\",{style:{color:'green'},children:\"(Optional)\"})]}),/*#__PURE__*/_jsx(\"input\",{type:\"url\",id:\"linkedin\",name:\"linkedin\",placeholder:\"https://www.linkedin.com/in/your-profile\"}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"submit-button\",children:\"SEND\"})]})]})});};export default Contact;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "Contact", "className", "children", "action", "method", "htmlFor", "type", "id", "name", "placeholder", "required", "style", "color"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/Contact.js"], "sourcesContent": ["import React from 'react';\n\nconst Contact = () => {\n  return (\n    <section className=\"contact\">\n      <div className=\"contact-overlay\">\n        <h2>LET'S CREATE TOGETHER</h2>\n        <form action=\"https://formspree.io/f/mblgroaz\" method=\"POST\">\n          <label htmlFor=\"name\">YOUR NAME</label>\n          <input type=\"text\" id=\"name\" name=\"name\" placeholder=\"NAME\" required />\n          \n          <label htmlFor=\"email\">YOUR EMAIL</label>\n          <input type=\"email\" id=\"email\" name=\"email\" placeholder=\"EMAIL\" required />\n          \n          <label htmlFor=\"services\">WHAT SERVICES ARE YOU LOOKING FOR?</label>\n          <input type=\"text\" id=\"services\" name=\"services\" placeholder=\"Web Design, Graphic design...\" required />\n          \n          <label htmlFor=\"message\">YOUR MESSAGE</label>\n          <textarea id=\"message\" name=\"message\" placeholder=\"YOUR MESSAGE\" required></textarea>\n          \n          <label htmlFor=\"linkedin\">Your LinkedIn <span style={{color: 'green'}}>(Optional)</span></label>\n          <input type=\"url\" id=\"linkedin\" name=\"linkedin\" placeholder=\"https://www.linkedin.com/in/your-profile\" />\n          \n          <button type=\"submit\" className=\"submit-button\">SEND</button>\n        </form>\n      </div>\n    </section>\n  );\n};\n\nexport default Contact;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,OAAO,CAAGA,CAAA,GAAM,CACpB,mBACEH,IAAA,YAASI,SAAS,CAAC,SAAS,CAAAC,QAAA,cAC1BH,KAAA,QAAKE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BL,IAAA,OAAAK,QAAA,CAAI,uBAAqB,CAAI,CAAC,cAC9BH,KAAA,SAAMI,MAAM,CAAC,iCAAiC,CAACC,MAAM,CAAC,MAAM,CAAAF,QAAA,eAC1DL,IAAA,UAAOQ,OAAO,CAAC,MAAM,CAAAH,QAAA,CAAC,WAAS,CAAO,CAAC,cACvCL,IAAA,UAAOS,IAAI,CAAC,MAAM,CAACC,EAAE,CAAC,MAAM,CAACC,IAAI,CAAC,MAAM,CAACC,WAAW,CAAC,MAAM,CAACC,QAAQ,MAAE,CAAC,cAEvEb,IAAA,UAAOQ,OAAO,CAAC,OAAO,CAAAH,QAAA,CAAC,YAAU,CAAO,CAAC,cACzCL,IAAA,UAAOS,IAAI,CAAC,OAAO,CAACC,EAAE,CAAC,OAAO,CAACC,IAAI,CAAC,OAAO,CAACC,WAAW,CAAC,OAAO,CAACC,QAAQ,MAAE,CAAC,cAE3Eb,IAAA,UAAOQ,OAAO,CAAC,UAAU,CAAAH,QAAA,CAAC,oCAAkC,CAAO,CAAC,cACpEL,IAAA,UAAOS,IAAI,CAAC,MAAM,CAACC,EAAE,CAAC,UAAU,CAACC,IAAI,CAAC,UAAU,CAACC,WAAW,CAAC,+BAA+B,CAACC,QAAQ,MAAE,CAAC,cAExGb,IAAA,UAAOQ,OAAO,CAAC,SAAS,CAAAH,QAAA,CAAC,cAAY,CAAO,CAAC,cAC7CL,IAAA,aAAUU,EAAE,CAAC,SAAS,CAACC,IAAI,CAAC,SAAS,CAACC,WAAW,CAAC,cAAc,CAACC,QAAQ,MAAW,CAAC,cAErFX,KAAA,UAAOM,OAAO,CAAC,UAAU,CAAAH,QAAA,EAAC,gBAAc,cAAAL,IAAA,SAAMc,KAAK,CAAE,CAACC,KAAK,CAAE,OAAO,CAAE,CAAAV,QAAA,CAAC,YAAU,CAAM,CAAC,EAAO,CAAC,cAChGL,IAAA,UAAOS,IAAI,CAAC,KAAK,CAACC,EAAE,CAAC,UAAU,CAACC,IAAI,CAAC,UAAU,CAACC,WAAW,CAAC,0CAA0C,CAAE,CAAC,cAEzGZ,IAAA,WAAQS,IAAI,CAAC,QAAQ,CAACL,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,MAAI,CAAQ,CAAC,EACzD,CAAC,EACJ,CAAC,CACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAAF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}