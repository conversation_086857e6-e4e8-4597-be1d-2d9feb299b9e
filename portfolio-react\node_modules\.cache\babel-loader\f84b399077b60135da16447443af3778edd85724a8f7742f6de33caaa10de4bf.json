{"ast": null, "code": "import React from'react';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const Footer=()=>{return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"footer\",{children:/*#__PURE__*/_jsx(\"p\",{children:\"2025 REECRAFT. ALL RIGHTS RESERVED.\"})}),/*#__PURE__*/_jsx(\"a\",{href:\"#\",className:\"back-to-top\",children:\"\\u2191\"})]});};export default Footer;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "Footer", "children", "href", "className"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/Footer.js"], "sourcesContent": ["import React from 'react';\n\nconst Footer = () => {\n  return (\n    <>\n      <footer>\n        <p>2025 REECRAFT. ALL RIGHTS RESERVED.</p>\n      </footer>\n      <a href=\"#\" className=\"back-to-top\">↑</a>\n    </>\n  );\n};\n\nexport default Footer;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,MAAM,CAAGA,CAAA,GAAM,CACnB,mBACED,KAAA,CAAAF,SAAA,EAAAI,QAAA,eACEN,IAAA,WAAAM,QAAA,cACEN,IAAA,MAAAM,QAAA,CAAG,qCAAmC,CAAG,CAAC,CACpC,CAAC,cACTN,IAAA,MAAGO,IAAI,CAAC,GAAG,CAACC,SAAS,CAAC,aAAa,CAAAF,QAAA,CAAC,QAAC,CAAG,CAAC,EACzC,CAAC,CAEP,CAAC,CAED,cAAe,CAAAD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}