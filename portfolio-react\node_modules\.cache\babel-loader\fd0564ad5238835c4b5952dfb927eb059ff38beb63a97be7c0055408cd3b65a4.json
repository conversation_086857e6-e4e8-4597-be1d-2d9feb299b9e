{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfulio\\\\portfolio-react\\\\src\\\\components\\\\SkillsTicker.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SkillsTicker = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"skills-ticker\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ticker-track\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"UI/UX DESIGN\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), \" \\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"GRAPHIC DESIGN\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 37\n      }, this), \" \\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"WEB DEVELOPMENT\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 67\n      }, this), \" \\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"UI/UX DESIGN\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 98\n      }, this), \" \\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"BRANDING\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 126\n      }, this), \" \\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"WEBSITE DESIGN\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 150\n      }, this), \" \\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"DIGITAL CREATIVITY\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 180\n      }, this), \" \\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"E-COMMERCE SOLUTIONS\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 214\n      }, this), \" \\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"CUSTOM DEVELOPMENT\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 250\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"UI/UX DESIGN\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), \" \\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"GRAPHIC DESIGN\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 37\n      }, this), \" \\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"WEB DEVELOPMENT\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 67\n      }, this), \" \\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"UI/UX DESIGN\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 98\n      }, this), \" \\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"BRANDING\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 126\n      }, this), \" \\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"WEBSITE DESIGN\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 150\n      }, this), \" \\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"DIGITAL CREATIVITY\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 180\n      }, this), \" \\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"E-COMMERCE SOLUTIONS\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 214\n      }, this), \" \\u2022 \", /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"CUSTOM DEVELOPMENT\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 250\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = SkillsTicker;\nexport default SkillsTicker;\nvar _c;\n$RefreshReg$(_c, \"SkillsTicker\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "SkillsTicker", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/SkillsTicker.js"], "sourcesContent": ["import React from 'react';\n\nconst SkillsTicker = () => {\n  return (\n    <div className=\"skills-ticker\">\n      <div className=\"ticker-track\">\n        <span>UI/UX DESIGN</span> • <span>GRAPHIC DESIGN</span> • <span>WEB DEVELOPMENT</span> • <span>UI/UX DESIGN</span> • <span>BRANDING</span> • <span>WEBSITE DESIGN</span> • <span>DIGITAL CREATIVITY</span> • <span>E-COMMERCE SOLUTIONS</span> • <span>CUSTOM DEVELOPMENT</span>\n        {/* Duplicate for infinite scroll */}\n        <span>UI/UX DESIGN</span> • <span>GRAPHIC DESIGN</span> • <span>WEB DEVELOPMENT</span> • <span>UI/UX DESIGN</span> • <span>BRANDING</span> • <span>WEBSITE DESIGN</span> • <span>DIGITAL CREATIVITY</span> • <span>E-COMMERCE SOLUTIONS</span> • <span>CUSTOM DEVELOPMENT</span>\n      </div>\n    </div>\n  );\n};\n\nexport default SkillsTicker;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACzB,oBACED,OAAA;IAAKE,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC5BH,OAAA;MAAKE,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BH,OAAA;QAAAG,QAAA,EAAM;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,YAAG,eAAAP,OAAA;QAAAG,QAAA,EAAM;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,YAAG,eAAAP,OAAA;QAAAG,QAAA,EAAM;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,YAAG,eAAAP,OAAA;QAAAG,QAAA,EAAM;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,YAAG,eAAAP,OAAA;QAAAG,QAAA,EAAM;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,YAAG,eAAAP,OAAA;QAAAG,QAAA,EAAM;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,YAAG,eAAAP,OAAA;QAAAG,QAAA,EAAM;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,YAAG,eAAAP,OAAA;QAAAG,QAAA,EAAM;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,YAAG,eAAAP,OAAA;QAAAG,QAAA,EAAM;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEhRP,OAAA;QAAAG,QAAA,EAAM;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,YAAG,eAAAP,OAAA;QAAAG,QAAA,EAAM;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,YAAG,eAAAP,OAAA;QAAAG,QAAA,EAAM;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,YAAG,eAAAP,OAAA;QAAAG,QAAA,EAAM;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,YAAG,eAAAP,OAAA;QAAAG,QAAA,EAAM;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,YAAG,eAAAP,OAAA;QAAAG,QAAA,EAAM;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,YAAG,eAAAP,OAAA;QAAAG,QAAA,EAAM;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,YAAG,eAAAP,OAAA;QAAAG,QAAA,EAAM;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,YAAG,eAAAP,OAAA;QAAAG,QAAA,EAAM;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7Q;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GAVIP,YAAY;AAYlB,eAAeA,YAAY;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}