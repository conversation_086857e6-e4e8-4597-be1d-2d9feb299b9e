import React from 'react';

const Contact = () => {
  return (
    <section className="contact">
      <div className="contact-overlay">
        <h2>LET'S CREATE TOGETHER</h2>
        <form action="https://formspree.io/f/mblgroaz" method="POST">
          <label htmlFor="name">YOUR NAME</label>
          <input type="text" id="name" name="name" placeholder="NAME" required />
          
          <label htmlFor="email">YOUR EMAIL</label>
          <input type="email" id="email" name="email" placeholder="EMAIL" required />
          
          <label htmlFor="services">WHAT SERVICES ARE YOU LOOKING FOR?</label>
          <input type="text" id="services" name="services" placeholder="Web Design, Graphic design..." required />
          
          <label htmlFor="message">YOUR MESSAGE</label>
          <textarea id="message" name="message" placeholder="YOUR MESSAGE" required></textarea>
          
          <label htmlFor="linkedin">Your LinkedIn <span style={{color: 'green'}}>(Optional)</span></label>
          <input type="url" id="linkedin" name="linkedin" placeholder="https://www.linkedin.com/in/your-profile" />
          
          <button type="submit" className="submit-button">SEND</button>
        </form>
      </div>
    </section>
  );
};

export default Contact;
